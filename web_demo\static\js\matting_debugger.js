/**
 * 高级背景抠图调试工具
 * 用于诊断和解决抠图功能问题
 */

class MattingDebugger {
    constructor() {
        this.debugInfo = {
            modules: {},
            elements: {},
            events: {},
            performance: {},
            errors: []
        };
        
        this.startTime = performance.now();
        this.checkInterval = null;
        
        console.log('抠图调试器初始化');
    }
    
    /**
     * 检查模块加载状态
     */
    checkModules() {
        const modules = [
            'AdvancedGreenScreen',
            'TransparentBackground', 
            'EdgeProcessing',
            'BackgroundReplacement',
            'AdvancedMattingController'
        ];
        
        console.log('=== 模块加载状态检查 ===');
        modules.forEach(moduleName => {
            const isLoaded = typeof window[moduleName] !== 'undefined';
            this.debugInfo.modules[moduleName] = isLoaded;
            console.log(`${moduleName}: ${isLoaded ? '✅ 已加载' : '❌ 未加载'}`);
        });
        
        return this.debugInfo.modules;
    }
    
    /**
     * 检查DOM元素状态
     */
    checkElements() {
        const elements = [
            'advancedMattingToggle',
            'colorToleranceSlider',
            'saturationSlider', 
            'brightnessSlider',
            'featherRadiusSlider',
            'antiAliasSlider',
            'smoothingSlider',
            'transparentBgBtn',
            'colorBgBtn',
            'imageBgBtn',
            'advancedProcessingFps'
        ];
        
        console.log('=== DOM元素状态检查 ===');
        elements.forEach(elementId => {
            const element = document.getElementById(elementId);
            const exists = !!element;
            this.debugInfo.elements[elementId] = {
                exists: exists,
                classes: exists ? element.className : null,
                attributes: exists ? Array.from(element.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', ') : null
            };
            
            console.log(`${elementId}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
            if (exists && element.className) {
                console.log(`  - 类名: ${element.className}`);
            }
        });
        
        return this.debugInfo.elements;
    }
    
    /**
     * 检查事件绑定状态
     */
    checkEvents() {
        console.log('=== 事件绑定状态检查 ===');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (toggle) {
            const hasControllerBinding = toggle.hasAttribute('data-controller-bound');
            const hasManualBinding = toggle.hasAttribute('data-event-bound');
            
            this.debugInfo.events.toggle = {
                controllerBinding: hasControllerBinding,
                manualBinding: hasManualBinding,
                totalListeners: this.getEventListenerCount(toggle, 'click')
            };
            
            console.log(`抠图开关事件绑定:`);
            console.log(`  - 控制器绑定: ${hasControllerBinding ? '✅' : '❌'}`);
            console.log(`  - 手动绑定: ${hasManualBinding ? '✅' : '❌'}`);
            
            // 测试点击事件
            console.log('测试点击事件...');
            const clickEvent = new Event('click');
            toggle.dispatchEvent(clickEvent);
            
        } else {
            console.log('❌ 抠图开关元素不存在');
        }
        
        return this.debugInfo.events;
    }
    
    /**
     * 获取事件监听器数量（近似）
     */
    getEventListenerCount(element, eventType) {
        // 这是一个近似方法，实际的监听器数量可能无法准确获取
        try {
            const listeners = getEventListeners ? getEventListeners(element) : null;
            return listeners && listeners[eventType] ? listeners[eventType].length : '未知';
        } catch (e) {
            return '无法检测';
        }
    }
    
    /**
     * 检查控制器实例状态
     */
    checkController() {
        console.log('=== 控制器实例状态检查 ===');
        
        const controller = window.advancedMattingController;
        if (controller) {
            console.log('✅ 控制器实例存在');
            console.log('控制器状态:', {
                enabled: controller.enabled,
                backgroundType: controller.currentBackgroundType,
                quality: controller.currentQuality
            });
            
            // 检查子模块
            console.log('子模块状态:');
            console.log('  - greenScreen:', !!controller.greenScreen);
            console.log('  - transparentBg:', !!controller.transparentBg);
            console.log('  - edgeProcessor:', !!controller.edgeProcessor);
            console.log('  - bgReplacement:', !!controller.bgReplacement);
            
            this.debugInfo.controller = {
                exists: true,
                enabled: controller.enabled,
                backgroundType: controller.currentBackgroundType,
                quality: controller.currentQuality,
                subModules: {
                    greenScreen: !!controller.greenScreen,
                    transparentBg: !!controller.transparentBg,
                    edgeProcessor: !!controller.edgeProcessor,
                    bgReplacement: !!controller.bgReplacement
                }
            };
            
        } else {
            console.log('❌ 控制器实例不存在');
            this.debugInfo.controller = { exists: false };
        }
        
        return this.debugInfo.controller;
    }
    
    /**
     * 性能检查
     */
    checkPerformance() {
        console.log('=== 性能状态检查 ===');
        
        const now = performance.now();
        const initTime = now - this.startTime;
        
        console.log(`初始化耗时: ${initTime.toFixed(2)}ms`);
        
        // 检查内存使用
        if (performance.memory) {
            const memory = performance.memory;
            console.log('内存使用:');
            console.log(`  - 已使用: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            console.log(`  - 总分配: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            console.log(`  - 限制: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`);
            
            this.debugInfo.performance = {
                initTime: initTime,
                memory: {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit
                }
            };
        }
        
        return this.debugInfo.performance;
    }
    
    /**
     * 运行完整诊断
     */
    runFullDiagnosis() {
        console.log('🔍 开始运行完整诊断...');
        console.log('时间:', new Date().toLocaleString());
        
        this.checkModules();
        this.checkElements();
        this.checkEvents();
        this.checkController();
        this.checkPerformance();
        
        console.log('=== 诊断总结 ===');
        
        // 检查关键问题
        const issues = [];
        
        // 检查模块加载
        const moduleNames = Object.keys(this.debugInfo.modules);
        const unloadedModules = moduleNames.filter(name => !this.debugInfo.modules[name]);
        if (unloadedModules.length > 0) {
            issues.push(`未加载的模块: ${unloadedModules.join(', ')}`);
        }
        
        // 检查关键元素
        if (!this.debugInfo.elements.advancedMattingToggle?.exists) {
            issues.push('抠图开关元素不存在');
        }
        
        // 检查控制器
        if (!this.debugInfo.controller?.exists) {
            issues.push('控制器实例不存在');
        }
        
        // 检查事件绑定
        if (!this.debugInfo.events.toggle?.controllerBinding && !this.debugInfo.events.toggle?.manualBinding) {
            issues.push('抠图开关事件未绑定');
        }
        
        if (issues.length > 0) {
            console.log('❌ 发现问题:');
            issues.forEach(issue => console.log(`  - ${issue}`));
        } else {
            console.log('✅ 未发现明显问题');
        }
        
        console.log('🔍 诊断完成');
        
        return {
            issues: issues,
            debugInfo: this.debugInfo
        };
    }
    
    /**
     * 尝试修复常见问题
     */
    attemptFix() {
        console.log('🔧 尝试修复常见问题...');
        
        const fixes = [];
        
        // 修复1: 重新绑定抠图开关事件
        const toggle = document.getElementById('advancedMattingToggle');
        if (toggle && window.advancedMattingController) {
            if (!toggle.hasAttribute('data-manual-fix-bound')) {
                toggle.addEventListener('click', function() {
                    console.log('🔧 修复绑定: 抠图开关被点击');
                    const controller = window.advancedMattingController;
                    if (controller) {
                        controller.enabled = !controller.enabled;
                        toggle.classList.toggle('active', controller.enabled);
                        console.log('🔧 修复绑定: 抠图状态', controller.enabled ? '启用' : '禁用');
                    }
                });
                toggle.setAttribute('data-manual-fix-bound', 'true');
                fixes.push('重新绑定抠图开关事件');
            }
        }
        
        // 修复2: 重新初始化控制器（如果模块已加载但实例不存在）
        if (!window.advancedMattingController && typeof AdvancedMattingController !== 'undefined') {
            try {
                window.advancedMattingController = new AdvancedMattingController();
                fixes.push('重新创建控制器实例');
            } catch (error) {
                console.error('重新创建控制器失败:', error);
            }
        }
        
        console.log(`🔧 应用了 ${fixes.length} 个修复:`);
        fixes.forEach(fix => console.log(`  - ${fix}`));
        
        return fixes;
    }
    
    /**
     * 开始持续监控
     */
    startMonitoring() {
        console.log('📊 开始持续监控...');
        
        this.checkInterval = setInterval(() => {
            const controller = window.advancedMattingController;
            if (controller && controller.enabled) {
                console.log('📊 监控状态:', {
                    enabled: controller.enabled,
                    fps: controller.currentFps || 'N/A',
                    backgroundType: controller.currentBackgroundType
                });
            }
        }, 5000);
    }
    
    /**
     * 停止监控
     */
    stopMonitoring() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
            console.log('📊 停止监控');
        }
    }
}

// 创建全局调试器实例
window.mattingDebugger = new MattingDebugger();

// 添加快捷调试命令
window.debugMatting = () => window.mattingDebugger.runFullDiagnosis();
window.fixMatting = () => window.mattingDebugger.attemptFix();

console.log('抠图调试器已加载。使用 debugMatting() 进行诊断，使用 fixMatting() 尝试修复。');
