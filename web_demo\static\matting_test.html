<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景抠图功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-section {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4361ee;
            border-bottom: 2px solid #4361ee;
            padding-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4cc9f0;
            font-weight: bold;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #4cc9f0; }
        .status-warning { background-color: #ffa500; }
        .status-error { background-color: #ff4757; }
        
        .test-button {
            background: #4361ee;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        
        .test-button:hover {
            background: #3f37c9;
            transform: translateY(-2px);
        }
        
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .result-success {
            background: #e8f5e8;
            border: 1px solid #4cc9f0;
            color: #2d5a2d;
        }
        
        .result-error {
            background: #ffe8e8;
            border: 1px solid #ff4757;
            color: #8b0000;
        }
        
        .compatibility-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .compatibility-item {
            padding: 15px;
            border-radius: 8px;
            background: white;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .demo-canvas {
            width: 100%;
            max-width: 300px;
            height: 200px;
            border: 2px solid #4361ee;
            border-radius: 8px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            margin: 10px auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎭 数字人背景抠图功能测试</h1>
        
        <div class="test-grid">
            <!-- 功能特性测试 -->
            <div class="test-section">
                <h3>功能特性</h3>
                <ul class="feature-list">
                    <li>AI智能抠图 (TensorFlow.js BodyPix)</li>
                    <li>绿幕抠图 (HSV色彩空间)</li>
                    <li>背景替换 (纯色/图片/视频)</li>
                    <li>边缘羽化处理</li>
                    <li>实时性能监控</li>
                    <li>自适应质量调节</li>
                    <li>预设模板 (人像/会议/游戏)</li>
                    <li>响应式设计</li>
                </ul>
            </div>
            
            <!-- 兼容性检测 -->
            <div class="test-section">
                <h3>兼容性检测</h3>
                <button class="test-button" onclick="runCompatibilityTest()">检测兼容性</button>
                <div class="compatibility-grid" id="compatibilityResults">
                    <!-- 兼容性结果将在这里显示 -->
                </div>
            </div>
            
            <!-- 性能测试 -->
            <div class="test-section">
                <h3>性能测试</h3>
                <button class="test-button" onclick="runPerformanceTest()">运行性能测试</button>
                <div class="demo-canvas" id="performanceDemo">
                    点击运行性能测试
                </div>
                <div id="performanceResults"></div>
            </div>
            
            <!-- 算法测试 -->
            <div class="test-section">
                <h3>算法测试</h3>
                <button class="test-button" onclick="testGreenScreen()">测试绿幕算法</button>
                <button class="test-button" onclick="testFeathering()">测试边缘羽化</button>
                <div id="algorithmResults"></div>
            </div>
        </div>
        
        <!-- 集成测试 -->
        <div class="test-section">
            <h3>集成测试</h3>
            <p>测试背景抠图功能与数字人系统的集成效果：</p>
            <button class="test-button" onclick="openMainDemo()">打开主演示页面</button>
            <button class="test-button" onclick="testIntegration()">测试集成功能</button>
            <div id="integrationResults"></div>
        </div>
    </div>

    <script>
        // 兼容性检测
        function runCompatibilityTest() {
            const tests = {
                'WebGL支持': () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        return !!gl;
                    } catch (e) {
                        return false;
                    }
                },
                'Canvas 2D': () => {
                    try {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext && canvas.getContext('2d'));
                    } catch (e) {
                        return false;
                    }
                },
                'File API': () => {
                    return !!(window.File && window.FileReader && window.FileList && window.Blob);
                },
                'Web Workers': () => {
                    return typeof Worker !== 'undefined';
                },
                'TensorFlow.js': () => {
                    return typeof tf !== 'undefined';
                },
                'BodyPix': () => {
                    return typeof bodyPix !== 'undefined';
                }
            };
            
            const resultsContainer = document.getElementById('compatibilityResults');
            resultsContainer.innerHTML = '';
            
            Object.entries(tests).forEach(([name, test]) => {
                const result = test();
                const item = document.createElement('div');
                item.className = 'compatibility-item';
                item.innerHTML = `
                    <div class="status-indicator ${result ? 'status-success' : 'status-error'}"></div>
                    <div>${name}</div>
                    <div style="font-size: 12px; color: #666;">${result ? '支持' : '不支持'}</div>
                `;
                resultsContainer.appendChild(item);
            });
        }
        
        // 性能测试
        function runPerformanceTest() {
            const demo = document.getElementById('performanceDemo');
            const results = document.getElementById('performanceResults');
            
            demo.textContent = '正在测试...';
            
            // 模拟性能测试
            const startTime = performance.now();
            let frameCount = 0;
            const maxFrames = 100;
            
            function testFrame() {
                frameCount++;
                
                // 模拟抠图处理
                const canvas = document.createElement('canvas');
                canvas.width = 640;
                canvas.height = 480;
                const ctx = canvas.getContext('2d');
                
                // 模拟图像处理
                const imageData = ctx.createImageData(640, 480);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] = Math.random() * 255;     // R
                    imageData.data[i + 1] = Math.random() * 255; // G
                    imageData.data[i + 2] = Math.random() * 255; // B
                    imageData.data[i + 3] = 255;                 // A
                }
                ctx.putImageData(imageData, 0, 0);
                
                if (frameCount < maxFrames) {
                    requestAnimationFrame(testFrame);
                } else {
                    const endTime = performance.now();
                    const totalTime = endTime - startTime;
                    const avgFrameTime = totalTime / maxFrames;
                    const fps = 1000 / avgFrameTime;
                    
                    demo.textContent = `测试完成: ${fps.toFixed(1)} FPS`;
                    
                    results.innerHTML = `
                        <div class="test-result ${fps >= 15 ? 'result-success' : 'result-error'}">
                            总时间: ${totalTime.toFixed(2)}ms<br>
                            平均帧时间: ${avgFrameTime.toFixed(2)}ms<br>
                            估算FPS: ${fps.toFixed(1)}<br>
                            性能等级: ${fps >= 20 ? '高' : fps >= 15 ? '中' : '低'}
                        </div>
                    `;
                }
            }
            
            requestAnimationFrame(testFrame);
        }
        
        // 绿幕算法测试
        function testGreenScreen() {
            const results = document.getElementById('algorithmResults');
            
            try {
                // 模拟绿幕算法测试
                const testPixels = [
                    [0, 255, 0],    // 纯绿色
                    [50, 200, 50],  // 绿色变体
                    [255, 0, 0],    // 红色
                    [0, 0, 255],    // 蓝色
                    [128, 128, 128] // 灰色
                ];
                
                let passCount = 0;
                testPixels.forEach(([r, g, b], index) => {
                    // 简化的HSV转换和绿色检测
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    const diff = max - min;
                    
                    let h = 0;
                    if (diff !== 0) {
                        if (max === r) h = ((g - b) / diff) % 6;
                        else if (max === g) h = (b - r) / diff + 2;
                        else h = (r - g) / diff + 4;
                    }
                    h = Math.round(h * 60);
                    if (h < 0) h += 360;
                    
                    const s = max === 0 ? 0 : diff / max;
                    const v = max / 255;
                    
                    const isGreen = (h >= 60 && h <= 180) && (s >= 0.3) && (v >= 0.3);
                    const shouldBeGreen = index < 2;
                    
                    if (isGreen === shouldBeGreen) passCount++;
                });
                
                results.innerHTML = `
                    <div class="test-result result-success">
                        绿幕算法测试: ${passCount}/${testPixels.length} 通过<br>
                        准确率: ${(passCount / testPixels.length * 100).toFixed(1)}%
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `
                    <div class="test-result result-error">
                        绿幕算法测试失败: ${error.message}
                    </div>
                `;
            }
        }
        
        // 边缘羽化测试
        function testFeathering() {
            const results = document.getElementById('algorithmResults');
            
            try {
                // 模拟羽化算法测试
                const testMask = new Uint8ClampedArray([
                    0, 0, 0, 255, 255,
                    0, 0, 255, 255, 255,
                    0, 255, 255, 255, 255,
                    255, 255, 255, 255, 255,
                    255, 255, 255, 255, 255
                ]);
                
                // 简化的羽化处理
                const width = 5, height = 5;
                const result = new Uint8ClampedArray(testMask.length);
                
                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        let sum = 0, count = 0;
                        
                        for (let ky = -1; ky <= 1; ky++) {
                            for (let kx = -1; kx <= 1; kx++) {
                                const ny = y + ky;
                                const nx = x + kx;
                                
                                if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                                    sum += testMask[ny * width + nx];
                                    count++;
                                }
                            }
                        }
                        
                        result[y * width + x] = Math.round(sum / count);
                    }
                }
                
                const smoothness = result.reduce((acc, val, idx) => {
                    if (idx > 0) {
                        return acc + Math.abs(val - result[idx - 1]);
                    }
                    return acc;
                }, 0) / (result.length - 1);
                
                results.innerHTML = `
                    <div class="test-result result-success">
                        边缘羽化测试完成<br>
                        平滑度指标: ${smoothness.toFixed(2)}<br>
                        状态: ${smoothness < 50 ? '良好' : '需要优化'}
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `
                    <div class="test-result result-error">
                        边缘羽化测试失败: ${error.message}
                    </div>
                `;
            }
        }
        
        // 打开主演示页面
        function openMainDemo() {
            window.open('MiniLive_RealTime.html', '_blank');
        }
        
        // 集成测试
        function testIntegration() {
            const results = document.getElementById('integrationResults');
            
            results.innerHTML = `
                <div class="test-result result-success">
                    集成测试说明：<br>
                    1. 点击"打开主演示页面"进入数字人系统<br>
                    2. 在控制面板中找到"背景抠图"配置区域<br>
                    3. 启用抠图功能并测试不同模式<br>
                    4. 验证性能监控和质量自适应功能<br>
                    5. 测试预设模板和背景替换效果
                </div>
            `;
        }
        
        // 页面加载时自动运行兼容性检测
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runCompatibilityTest, 500);
        });
    </script>
</body>
</html>
