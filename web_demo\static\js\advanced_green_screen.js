/**
 * 高级绿幕抠图模块
 * 基于HSV色彩空间实现高精度绿幕背景移除
 */

class AdvancedGreenScreen {
    constructor() {
        // 绿幕抠图参数
        this.hueRange = [60, 180];        // 绿色色相范围 (度)
        this.saturationMin = 0.3;         // 最小饱和度阈值
        this.valueMin = 0.2;              // 最小明度阈值
        this.colorTolerance = 20;         // 色彩容差 (度)
        this.edgeFeather = 3;             // 边缘羽化半径
        this.spill = 0.1;                 // 绿色溢出处理强度
        
        // 性能参数
        this.quality = 'medium';          // 质量等级: low, medium, high
        this.enableSpillSuppression = true; // 启用绿色溢出抑制
        this.enableEdgeSmoothing = true;  // 启用边缘平滑
        
        // 工作画布
        this.workCanvas = document.createElement('canvas');
        this.workCtx = this.workCanvas.getContext('2d');
        this.maskCanvas = document.createElement('canvas');
        this.maskCtx = this.maskCanvas.getContext('2d');
        
        // 性能监控
        this.processingTimes = [];
        this.frameCount = 0;
    }
    
    /**
     * RGB转HSV色彩空间
     * @param {number} r - 红色分量 (0-255)
     * @param {number} g - 绿色分量 (0-255)
     * @param {number} b - 蓝色分量 (0-255)
     * @returns {Array} [h, s, v] - 色相(0-360), 饱和度(0-1), 明度(0-1)
     */
    rgbToHsv(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        const diff = max - min;
        
        let h = 0;
        if (diff !== 0) {
            if (max === r) {
                h = ((g - b) / diff) % 6;
            } else if (max === g) {
                h = (b - r) / diff + 2;
            } else {
                h = (r - g) / diff + 4;
            }
        }
        h = Math.round(h * 60);
        if (h < 0) h += 360;
        
        const s = max === 0 ? 0 : diff / max;
        const v = max;
        
        return [h, s, v];
    }
    
    /**
     * 检测像素是否为绿幕背景
     * @param {number} r - 红色分量
     * @param {number} g - 绿色分量  
     * @param {number} b - 蓝色分量
     * @returns {number} 0-1之间的透明度值，0=完全透明(背景)，1=完全不透明(前景)
     */
    isGreenScreen(r, g, b) {
        const [h, s, v] = this.rgbToHsv(r, g, b);
        
        // 检查是否在绿色色相范围内
        const hueInRange = (h >= this.hueRange[0] && h <= this.hueRange[1]) ||
                          (h >= this.hueRange[0] - this.colorTolerance && h <= this.hueRange[0]) ||
                          (h >= this.hueRange[1] && h <= this.hueRange[1] + this.colorTolerance);
        
        // 检查饱和度和明度
        const saturationOk = s >= this.saturationMin;
        const valueOk = v >= this.valueMin;
        
        if (hueInRange && saturationOk && valueOk) {
            // 计算绿幕匹配强度
            const hueDistance = Math.min(
                Math.abs(h - 120), // 距离标准绿色的距离
                Math.abs(h - 120 + 360),
                Math.abs(h - 120 - 360)
            );
            
            // 基于距离计算透明度
            const hueWeight = Math.max(0, 1 - hueDistance / this.colorTolerance);
            const satWeight = Math.min(1, (s - this.saturationMin) / (1 - this.saturationMin));
            const valWeight = Math.min(1, (v - this.valueMin) / (1 - this.valueMin));
            
            const transparency = hueWeight * satWeight * valWeight;
            return 1 - transparency; // 返回不透明度
        }
        
        return 1; // 完全不透明(前景)
    }
    
    /**
     * 绿色溢出抑制
     * 减少前景物体上的绿色反射
     */
    suppressGreenSpill(imageData, maskData, width, height) {
        if (!this.enableSpillSuppression) return;
        
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const pixelIndex = Math.floor(i / 4);
            const alpha = maskData[pixelIndex];
            
            // 只处理前景像素
            if (alpha > 0.1) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                
                // 检测绿色溢出
                if (g > r && g > b) {
                    const spillAmount = (g - Math.max(r, b)) / 255;
                    
                    if (spillAmount > this.spill) {
                        // 减少绿色分量
                        const reduction = spillAmount * this.spill * 255;
                        data[i + 1] = Math.max(0, g - reduction);
                        
                        // 轻微增加红色和蓝色以保持亮度
                        data[i] = Math.min(255, r + reduction * 0.3);
                        data[i + 2] = Math.min(255, b + reduction * 0.3);
                    }
                }
            }
        }
    }
    
    /**
     * 边缘平滑处理
     * 使用形态学操作平滑抠图边缘
     */
    smoothEdges(maskData, width, height) {
        if (!this.enableEdgeSmoothing) return maskData;
        
        const result = new Float32Array(maskData.length);
        const kernelSize = 3;
        const radius = Math.floor(kernelSize / 2);
        
        // 先进行轻微的开运算（腐蚀后膨胀）
        const eroded = this.morphologyErode(maskData, width, height, radius);
        const opened = this.morphologyDilate(eroded, width, height, radius);
        
        // 然后进行高斯模糊
        return this.gaussianBlur(opened, width, height, 1);
    }
    
    /**
     * 形态学腐蚀操作
     */
    morphologyErode(data, width, height, radius) {
        const result = new Float32Array(data.length);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let minValue = 1;
                
                for (let ky = -radius; ky <= radius; ky++) {
                    for (let kx = -radius; kx <= radius; kx++) {
                        const ny = Math.max(0, Math.min(height - 1, y + ky));
                        const nx = Math.max(0, Math.min(width - 1, x + kx));
                        minValue = Math.min(minValue, data[ny * width + nx]);
                    }
                }
                
                result[y * width + x] = minValue;
            }
        }
        
        return result;
    }
    
    /**
     * 形态学膨胀操作
     */
    morphologyDilate(data, width, height, radius) {
        const result = new Float32Array(data.length);
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let maxValue = 0;
                
                for (let ky = -radius; ky <= radius; ky++) {
                    for (let kx = -radius; kx <= radius; kx++) {
                        const ny = Math.max(0, Math.min(height - 1, y + ky));
                        const nx = Math.max(0, Math.min(width - 1, x + kx));
                        maxValue = Math.max(maxValue, data[ny * width + nx]);
                    }
                }
                
                result[y * width + x] = maxValue;
            }
        }
        
        return result;
    }
    
    /**
     * 高斯模糊
     */
    gaussianBlur(data, width, height, radius) {
        if (radius <= 0) return data;
        
        const result = new Float32Array(data.length);
        const kernelSize = radius * 2 + 1;
        const kernel = [];
        
        // 创建高斯核
        const sigma = radius / 3;
        let sum = 0;
        for (let i = 0; i < kernelSize; i++) {
            const x = i - radius;
            const value = Math.exp(-(x * x) / (2 * sigma * sigma));
            kernel.push(value);
            sum += value;
        }
        
        // 归一化核
        for (let i = 0; i < kernel.length; i++) {
            kernel[i] /= sum;
        }
        
        // 水平模糊
        const temp = new Float32Array(width * height);
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let value = 0;
                for (let k = 0; k < kernelSize; k++) {
                    const px = Math.max(0, Math.min(width - 1, x + k - radius));
                    value += data[y * width + px] * kernel[k];
                }
                temp[y * width + x] = value;
            }
        }
        
        // 垂直模糊
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let value = 0;
                for (let k = 0; k < kernelSize; k++) {
                    const py = Math.max(0, Math.min(height - 1, y + k - radius));
                    value += temp[py * width + x] * kernel[k];
                }
                result[y * width + x] = Math.max(0, Math.min(1, value));
            }
        }
        
        return result;
    }
    
    /**
     * 设置绿幕抠图参数
     */
    setParameters(params) {
        if (params.hueRange) this.hueRange = params.hueRange;
        if (params.saturationMin !== undefined) this.saturationMin = params.saturationMin;
        if (params.valueMin !== undefined) this.valueMin = params.valueMin;
        if (params.colorTolerance !== undefined) this.colorTolerance = params.colorTolerance;
        if (params.edgeFeather !== undefined) this.edgeFeather = params.edgeFeather;
        if (params.spill !== undefined) this.spill = params.spill;
        if (params.quality) this.quality = params.quality;
    }
    
    /**
     * 获取当前参数
     */
    getParameters() {
        return {
            hueRange: this.hueRange,
            saturationMin: this.saturationMin,
            valueMin: this.valueMin,
            colorTolerance: this.colorTolerance,
            edgeFeather: this.edgeFeather,
            spill: this.spill,
            quality: this.quality
        };
    }

    /**
     * 主要处理方法：对图像进行绿幕抠图
     * @param {ImageData} imageData - 输入图像数据
     * @param {number} width - 图像宽度
     * @param {number} height - 图像高度
     * @returns {Object} {imageData: 处理后的图像, maskData: 透明度遮罩}
     */
    process(imageData, width, height) {
        const startTime = performance.now();

        // 设置工作画布尺寸
        this.workCanvas.width = width;
        this.workCanvas.height = height;
        this.maskCanvas.width = width;
        this.maskCanvas.height = height;

        const data = imageData.data;
        const maskData = new Float32Array(width * height);

        // 第一步：生成初始透明度遮罩
        for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const pixelIndex = Math.floor(i / 4);
            maskData[pixelIndex] = this.isGreenScreen(r, g, b);
        }

        // 第二步：边缘平滑处理
        const smoothedMask = this.smoothEdges(maskData, width, height);

        // 第三步：边缘羽化
        const featheredMask = this.edgeFeather > 0 ?
            this.gaussianBlur(smoothedMask, width, height, this.edgeFeather) :
            smoothedMask;

        // 第四步：绿色溢出抑制
        this.suppressGreenSpill(imageData, featheredMask, width, height);

        // 第五步：应用透明度遮罩
        for (let i = 0; i < data.length; i += 4) {
            const pixelIndex = Math.floor(i / 4);
            const alpha = featheredMask[pixelIndex];
            data[i + 3] = Math.round(alpha * 255); // 设置Alpha通道
        }

        // 性能统计
        const processingTime = performance.now() - startTime;
        this.processingTimes.push(processingTime);
        if (this.processingTimes.length > 30) {
            this.processingTimes.shift();
        }
        this.frameCount++;

        return {
            imageData: imageData,
            maskData: featheredMask,
            processingTime: processingTime
        };
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (this.processingTimes.length === 0) {
            return { avgTime: 0, fps: 0, frameCount: this.frameCount };
        }

        const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
        const fps = Math.round(1000 / avgTime);

        return {
            avgTime: avgTime.toFixed(2),
            fps: fps,
            frameCount: this.frameCount
        };
    }

    /**
     * 重置性能统计
     */
    resetStats() {
        this.processingTimes = [];
        this.frameCount = 0;
    }

    /**
     * 根据质量等级调整参数
     */
    adjustQuality(quality) {
        this.quality = quality;

        switch (quality) {
            case 'low':
                this.enableEdgeSmoothing = false;
                this.enableSpillSuppression = false;
                this.edgeFeather = Math.max(0, this.edgeFeather - 2);
                break;

            case 'medium':
                this.enableEdgeSmoothing = true;
                this.enableSpillSuppression = false;
                break;

            case 'high':
                this.enableEdgeSmoothing = true;
                this.enableSpillSuppression = true;
                break;
        }
    }

    /**
     * 自动调整参数以优化性能
     */
    autoOptimize() {
        const stats = this.getPerformanceStats();

        if (stats.fps < 10 && this.quality !== 'low') {
            this.adjustQuality('low');
            console.log('性能优化：切换到低质量模式');
        } else if (stats.fps < 15 && this.quality === 'high') {
            this.adjustQuality('medium');
            console.log('性能优化：切换到中等质量模式');
        } else if (stats.fps > 25 && this.quality !== 'high') {
            this.adjustQuality('high');
            console.log('性能优化：切换到高质量模式');
        }
    }
}

// 导出模块
window.AdvancedGreenScreen = AdvancedGreenScreen;
