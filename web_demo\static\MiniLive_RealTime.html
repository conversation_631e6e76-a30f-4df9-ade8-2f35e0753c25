<!doctype html>
<html lang="en-us">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <meta name="viewport" content="width=device-width, height=device-height, user-scalable=0"/>
        <link rel="icon" href="common/favicon.ico" type="image/x-icon">
        <title>MiniLive - 实时数字人</title>
        <style>
            :root {
                --primary-color: #4361ee;
                --secondary-color: #3f37c9;
                --accent-color: #f72585;
                --light-color: #f8f9fa;
                --dark-color: #212529;
                --success-color: #4cc9f0;
                --border-radius: 12px;
                --box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            }
            
            body {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
                background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);
                font-family: 'Arial', sans-serif;
                overflow: hidden;
                transition: background 0.5s ease;
            }

            video, canvas {
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
            }

            #canvas_video {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            #canvas_gl {
                position: absolute;
                top: -9999px;
                left: -9999px;
                width: 128px;
                height: 128px;
            }

            #screen {
                position: absolute;
                bottom: -1000;
                right: -1000;
                width: 1px;
                height: 1px;
            }

            #screen2 {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                border: none;
                z-index: 5;
            }

            #startMessage {
                position: absolute;
                top: 60%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-size: 24px;
                font-weight: bold;
                color: var(--dark-color);
                z-index: 10;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 15px 30px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
            }

            /* 控制面板容器 */
            #controlPanel {
                position: absolute;
                top: 20px;
                left: 20px;
                z-index: 10;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 15px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                display: flex;
                flex-direction: column;
                gap: 15px;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.5);
                max-height: 80vh;
                overflow-y: auto;
            }

            .control-group {
                display: flex;
                flex-direction: column;
            }

            .custom-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                padding: 12px 40px 12px 20px;
                font-size: 16px;
                font-weight: bold;
                color: var(--dark-color);
                background-color: #fff;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
                width: 180px;
            }

            .custom-select:hover {
                border-color: var(--secondary-color);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
                transform: translateY(-2px);
            }

            .custom-select:focus {
                border-color: var(--accent-color);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
            }

            .select-label {
                display: block;
                margin-bottom: 8px;
                font-weight: bold;
                color: var(--dark-color);
            }

            .control-group::after {
                content: '▼';
                position: absolute;
                right: 25px;
                transform: translateY(32px);
                pointer-events: none;
                color: var(--primary-color);
                font-size: 12px;
            }

            /* 背景色配置样式 */
            .background-config {
                border-top: 1px solid rgba(0, 0, 0, 0.1);
                padding-top: 15px;
                margin-top: 10px;
            }

            .color-input-group {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
            }

            #colorPicker {
                width: 50px;
                height: 40px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                cursor: pointer;
                outline: none;
                transition: all 0.3s ease;
            }

            #colorPicker:hover {
                border-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            #hexInput {
                flex: 1;
                padding: 10px 15px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                font-size: 14px;
                outline: none;
                transition: all 0.3s ease;
            }

            #hexInput:focus {
                border-color: var(--accent-color);
                box-shadow: 0 0 8px rgba(247, 37, 133, 0.4);
            }

            .preset-colors {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
                margin-bottom: 10px;
            }

            .preset-color-btn {
                width: 35px;
                height: 35px;
                border: 2px solid #fff;
                border-radius: var(--border-radius);
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .preset-color-btn:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }

            .preset-color-btn.active {
                border-color: var(--accent-color);
                border-width: 3px;
            }

            .control-buttons {
                display: flex;
                gap: 8px;
            }

            .bg-control-btn {
                flex: 1;
                padding: 8px 12px;
                border: none;
                border-radius: var(--border-radius);
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .reset-btn {
                background-color: var(--primary-color);
                color: white;
            }

            .reset-btn:hover {
                background-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            .random-btn {
                background-color: var(--success-color);
                color: white;
            }

            .random-btn:hover {
                background-color: #3ba3cc;
                transform: translateY(-2px);
            }

            /* 背景抠图配置样式 */
            .matting-config {
                border-top: 1px solid rgba(0, 0, 0, 0.1);
                padding-top: 15px;
                margin-top: 10px;
            }

            .matting-toggle {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 15px;
            }

            .toggle-switch {
                position: relative;
                width: 50px;
                height: 25px;
                background-color: #ccc;
                border-radius: 25px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            .toggle-switch.active {
                background-color: var(--primary-color);
            }

            .toggle-slider {
                position: absolute;
                top: 2px;
                left: 2px;
                width: 21px;
                height: 21px;
                background-color: white;
                border-radius: 50%;
                transition: transform 0.3s;
            }

            .toggle-switch.active .toggle-slider {
                transform: translateX(25px);
            }

            .matting-mode-select {
                margin-bottom: 15px;
            }

            .mode-buttons {
                display: flex;
                gap: 5px;
                margin-bottom: 10px;
            }

            .mode-btn {
                flex: 1;
                padding: 8px 12px;
                border: 2px solid var(--primary-color);
                background: white;
                color: var(--primary-color);
                border-radius: var(--border-radius);
                font-size: 12px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .mode-btn.active {
                background: var(--primary-color);
                color: white;
            }

            .mode-btn:hover {
                transform: translateY(-1px);
            }

            .slider-group {
                margin-bottom: 15px;
            }

            .slider-label {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
                font-size: 12px;
                color: var(--dark-color);
            }

            .custom-slider {
                width: 100%;
                height: 6px;
                border-radius: 3px;
                background: #ddd;
                outline: none;
                -webkit-appearance: none;
                appearance: none;
            }

            .custom-slider::-webkit-slider-thumb {
                -webkit-appearance: none;
                appearance: none;
                width: 18px;
                height: 18px;
                border-radius: 50%;
                background: var(--primary-color);
                cursor: pointer;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .custom-slider::-moz-range-thumb {
                width: 18px;
                height: 18px;
                border-radius: 50%;
                background: var(--primary-color);
                cursor: pointer;
                border: 2px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }

            .background-options {
                margin-bottom: 15px;
            }

            .bg-type-buttons {
                display: flex;
                gap: 5px;
                margin-bottom: 10px;
            }

            .bg-type-btn {
                flex: 1;
                padding: 6px 8px;
                border: 2px solid var(--success-color);
                background: white;
                color: var(--success-color);
                border-radius: var(--border-radius);
                font-size: 11px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .bg-type-btn.active {
                background: var(--success-color);
                color: white;
            }

            .bg-upload-area {
                border: 2px dashed #ccc;
                border-radius: var(--border-radius);
                padding: 15px;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s ease;
                margin-bottom: 10px;
            }

            .bg-upload-area:hover {
                border-color: var(--primary-color);
                background-color: rgba(67, 97, 238, 0.05);
            }

            .bg-upload-area.dragover {
                border-color: var(--accent-color);
                background-color: rgba(247, 37, 133, 0.1);
            }

            .upload-text {
                font-size: 12px;
                color: #666;
                margin-bottom: 5px;
            }

            .file-input {
                display: none;
            }

            .performance-info {
                background: rgba(0, 0, 0, 0.05);
                padding: 8px;
                border-radius: var(--border-radius);
                font-size: 11px;
                color: #666;
                margin-top: 10px;
            }

            #loadingSpinner {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 20;
                background-color: rgba(255, 255, 255, 0.9);
                padding: 25px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                text-align: center;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }

            #loadingSpinner strong {
                display: block;
                margin-bottom: 15px;
                color: var(--primary-color);
                font-size: 20px;
            }

            .spinner {
                border: 4px solid rgba(0, 0, 0, 0.1);
                border-radius: 50%;
                border-top: 4px solid var(--primary-color);
                width: 50px;
                height: 50px;
                margin: 0 auto;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            #canvasEl {
                position: absolute;
                left: -9999px;
                top: -9999px;
                width: 300px;
                height: 150px;
            }

            /* 弹幕容器 */
            #danmakuContainer {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                overflow: hidden;
                z-index: 8;
            }

            /* 弹幕样式 */
            .danmaku {
                position: absolute;
                white-space: nowrap;
                font-weight: bold;
                color: white;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                animation-name: danmaku-move;
                animation-timing-function: linear;
                animation-fill-mode: forwards;
                user-select: none;
            }

            @keyframes danmaku-move {
                from { transform: translateX(100%); }
                to { transform: translateX(-100%); }
            }

            /* 弹幕发送区域 - 移到右上角 */
            #danmakuInputContainer {
                position: absolute;
                top: 20px;
                right: 20px;
                z-index: 15;
                display: flex;
                align-items: center;
                background-color: rgba(255, 255, 255, 0.85);
                padding: 10px;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                transition: all 0.3s ease;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }

            #danmakuInput {
                width: 180px;
                padding: 10px 15px;
                border: 2px solid var(--primary-color);
                border-radius: var(--border-radius);
                font-size: 16px;
                outline: none;
                transition: all 0.3s ease;
            }

            #danmakuInput:focus {
                border-color: var(--accent-color);
                box-shadow: 0 0 8px rgba(247, 37, 133, 0.4);
            }

            #sendDanmakuBtn {
                margin-left: 10px;
                padding: 10px 15px;
                background-color: var(--primary-color);
                color: white;
                border: none;
                border-radius: var(--border-radius);
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            #sendDanmakuBtn:hover {
                background-color: var(--secondary-color);
                transform: translateY(-2px);
            }

            @media (max-width: 768px) {
                #controlPanel {
                    padding: 10px;
                    max-width: calc(100vw - 40px);
                }

                .custom-select {
                    padding: 10px 30px 10px 15px;
                    font-size: 14px;
                    width: 150px;
                }

                #danmakuInputContainer {
                    top: 20px;
                    right: 20px;
                    flex-direction: column;
                    width: auto;
                }

                #danmakuInput {
                    width: 120px;
                    margin-bottom: 10px;
                }

                #sendDanmakuBtn {
                    width: 100%;
                    margin-left: 0;
                    padding: 8px 10px;
                }

                /* 背景色配置移动端优化 */
                .background-config {
                    padding-top: 10px;
                    margin-top: 8px;
                }

                .color-input-group {
                    gap: 8px;
                    margin-bottom: 8px;
                }

                #colorPicker {
                    width: 40px;
                    height: 35px;
                }

                #hexInput {
                    font-size: 14px;
                    padding: 8px 12px;
                }

                .preset-colors {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 6px;
                    margin-bottom: 8px;
                }

                .preset-color-btn {
                    width: 30px;
                    height: 30px;
                }

                .control-buttons {
                    gap: 6px;
                }

                .bg-control-btn {
                    padding: 6px 10px;
                    font-size: 12px;
                }

                .select-label {
                    font-size: 14px;
                    margin-bottom: 6px;
                }

                /* 背景抠图移动端优化 */
                .matting-config {
                    padding-top: 10px;
                    margin-top: 8px;
                }

                .matting-toggle {
                    gap: 8px;
                    margin-bottom: 12px;
                }

                .toggle-switch {
                    width: 45px;
                    height: 22px;
                }

                .toggle-slider {
                    width: 18px;
                    height: 18px;
                }

                .toggle-switch.active .toggle-slider {
                    transform: translateX(23px);
                }

                .mode-btn {
                    padding: 6px 8px;
                    font-size: 11px;
                }

                .bg-type-btn {
                    padding: 5px 6px;
                    font-size: 10px;
                }

                .slider-group {
                    margin-bottom: 12px;
                }

                .slider-label {
                    font-size: 11px;
                }

                .bg-upload-area {
                    padding: 12px;
                }

                .upload-text {
                    font-size: 11px;
                }

                .performance-info {
                    padding: 6px;
                    font-size: 10px;
                }
            }
        </style>
    </head>
    <body>
        <!-- 控制面板 - 将角色和声音选择放在同一侧 -->
        <div id="controlPanel">
            <div class="control-group">
                <label for="characterDropdown" class="select-label">选择角色</label>
                <select id="characterDropdown" class="custom-select">
                    <option value="assets">女性1</option>
                    <option value="assets12">女性2</option>
                    <option value="assets13">女性3</option>
                    <option value="assets14">女性4</option>
                    <option value="assets4">女性5</option>
                    <option value="assets8">女性6</option>
                    <option value="assets2">女性7</option>
                    <option value="assets7">女性8</option>
                    <!-- <option value="assets7">女性四</option> -->
                    <option value="assets5">男性一</option>
                </select>
            </div>
            <div class="control-group">
                <label for="voiceDropdown" class="select-label">选择声音</label>
                <select id="voiceDropdown" class="custom-select">
                    <option value=0>温柔女</option>
                    <option value=1>温柔男</option>
                    <option value=2>甜美女</option>
                    <option value=3>青年女</option>
                    <option value=4>磁性男</option>
                </select>
            </div>

            <!-- 背景色配置区域 -->
            <div class="background-config">
                <label class="select-label">背景色配置</label>

                <!-- 颜色选择器和HEX输入 -->
                <div class="color-input-group">
                    <input type="color" id="colorPicker" value="#8EC5FC" title="选择背景色">
                    <input type="text" id="hexInput" placeholder="#8EC5FC" maxlength="7" title="输入HEX颜色值">
                </div>

                <!-- 预设颜色按钮 -->
                <div class="preset-colors">
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)"
                         style="background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);"
                         title="默认渐变"></div>
                    <div class="preset-color-btn" data-bg="#FF6B6B"
                         style="background: #FF6B6B;"
                         title="珊瑚红"></div>
                    <div class="preset-color-btn" data-bg="#4ECDC4"
                         style="background: #4ECDC4;"
                         title="青绿色"></div>
                    <div class="preset-color-btn" data-bg="#45B7D1"
                         style="background: #45B7D1;"
                         title="天蓝色"></div>
                    <div class="preset-color-btn" data-bg="#96CEB4"
                         style="background: #96CEB4;"
                         title="薄荷绿"></div>
                    <div class="preset-color-btn" data-bg="#FFEAA7"
                         style="background: #FFEAA7;"
                         title="柠檬黄"></div>
                    <div class="preset-color-btn" data-bg="#DDA0DD"
                         style="background: #DDA0DD;"
                         title="紫罗兰"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%)"
                         style="background: linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%);"
                         title="粉色渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                         style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"
                         title="紫蓝渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
                         style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"
                         title="粉红渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
                         style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);"
                         title="蓝色渐变"></div>
                    <div class="preset-color-btn" data-bg="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)"
                         style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);"
                         title="绿色渐变"></div>
                </div>

                <!-- 控制按钮 -->
                <div class="control-buttons">
                    <button class="bg-control-btn reset-btn" id="resetBgBtn" title="重置为默认背景">重置</button>
                    <button class="bg-control-btn random-btn" id="randomBgBtn" title="随机背景色">随机</button>
                </div>
            </div>

            <!-- 高级背景抠图配置区域 -->
            <div class="matting-config">
                <label class="select-label">🎭 高级背景抠图</label>

                <!-- 抠图开关 -->
                <div class="matting-toggle">
                    <span style="font-size: 14px; font-weight: bold;">启用抠图</span>
                    <div class="toggle-switch" id="advancedMattingToggle">
                        <div class="toggle-slider"></div>
                    </div>
                </div>

                <!-- 绿幕参数调节 -->
                <div class="matting-mode-select">
                    <div class="slider-label">
                        <span>🎨 绿幕参数</span>
                    </div>

                    <!-- 色彩容差 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>色彩容差</span>
                            <span id="colorToleranceValue">20</span>
                        </div>
                        <input type="range" class="custom-slider" id="colorToleranceSlider"
                               min="5" max="50" step="1" value="20">
                    </div>

                    <!-- 饱和度阈值 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>饱和度阈值</span>
                            <span id="saturationValue">0.3</span>
                        </div>
                        <input type="range" class="custom-slider" id="saturationSlider"
                               min="0.1" max="1.0" step="0.1" value="0.3">
                    </div>

                    <!-- 明度阈值 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>明度阈值</span>
                            <span id="brightnessValue">0.2</span>
                        </div>
                        <input type="range" class="custom-slider" id="brightnessSlider"
                               min="0.1" max="1.0" step="0.1" value="0.2">
                    </div>
                </div>

                <!-- 边缘处理 -->
                <div class="matting-mode-select">
                    <div class="slider-label">
                        <span>✨ 边缘处理</span>
                    </div>

                    <!-- 羽化半径 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>羽化半径</span>
                            <span id="featherRadiusValue">3</span>
                        </div>
                        <input type="range" class="custom-slider" id="featherRadiusSlider"
                               min="0" max="10" step="1" value="3">
                    </div>

                    <!-- 抗锯齿强度 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>抗锯齿强度</span>
                            <span id="antiAliasValue">0.5</span>
                        </div>
                        <input type="range" class="custom-slider" id="antiAliasSlider"
                               min="0" max="1.0" step="0.1" value="0.5">
                    </div>

                    <!-- 平滑迭代 -->
                    <div class="slider-group">
                        <div class="slider-label">
                            <span>平滑迭代</span>
                            <span id="smoothingValue">2</span>
                        </div>
                        <input type="range" class="custom-slider" id="smoothingSlider"
                               min="0" max="5" step="1" value="2">
                    </div>
                </div>

                <!-- 背景选择 -->
                <div class="background-options">
                    <div class="slider-label">
                        <span>🖼️ 背景类型</span>
                    </div>
                    <div class="bg-type-buttons">
                        <button class="bg-type-btn active" data-type="transparent" id="transparentBgBtn">透明</button>
                        <button class="bg-type-btn" data-type="color" id="colorBgBtn">纯色</button>
                        <button class="bg-type-btn" data-type="image" id="imageBgBtn">图片</button>
                    </div>

                    <!-- 颜色选择器 -->
                    <div class="color-input-group" id="colorInputGroup" style="display: none;">
                        <input type="color" id="bgColorPicker" value="#00FF00" title="选择背景色">
                        <input type="text" id="bgHexInput" placeholder="#00FF00" maxlength="7" title="输入HEX颜色值">
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="bg-upload-area" id="advancedUploadArea" style="display: none;">
                        <div class="upload-text">📁 点击或拖拽图片到此处</div>
                        <div style="font-size: 10px; color: #999;">支持 JPG, PNG, WebP 格式</div>
                        <input type="file" class="file-input" id="advancedBgFileInput" accept="image/*">
                    </div>
                </div>

                <!-- 质量控制 -->
                <div class="background-options">
                    <div class="slider-label">
                        <span>⚙️ 质量控制</span>
                    </div>
                    <div class="bg-type-buttons">
                        <button class="bg-type-btn" data-quality="low" id="lowQualityBtn">低</button>
                        <button class="bg-type-btn active" data-quality="medium" id="mediumQualityBtn">中</button>
                        <button class="bg-type-btn" data-quality="high" id="highQualityBtn">高</button>
                    </div>
                </div>

                <!-- 导出功能 -->
                <div class="control-buttons">
                    <button class="bg-control-btn reset-btn" id="exportPngBtn" title="导出PNG图片">导出PNG</button>
                    <button class="bg-control-btn random-btn" id="resetMattingBtn" title="重置所有设置">重置</button>
                </div>

                <!-- 性能信息 -->
                <div class="performance-info" id="advancedPerformanceInfo">
                    <div>处理帧率: <span id="advancedProcessingFps">--</span> FPS</div>
                    <div>边缘质量: <span id="edgeQuality">中等</span></div>
                    <div>背景类型: <span id="currentBgType">透明</span></div>
                </div>
            </div>
        </div>

        <figure id="loadingSpinner">
            <strong>MiniMates 加载中...</strong>
            <div class="spinner"></div>
        </figure>
        <canvas id="canvasEl"></canvas>
        <canvas id="canvas_video"></canvas>
        <canvas id="canvas_gl" width="128" height="128"></canvas>
        <div id="screen"></div>
        <iframe id="screen2" src="dialog_RealTime.html" frameborder="0" style="display: none;"></iframe>
        <div id="startMessage">加载中</div>

        <!-- 弹幕容器 -->
        <div id="danmakuContainer"></div>

        <!-- 弹幕输入区域 - 移到右上角 -->
        <div id="danmakuInputContainer">
            <input type="text" id="danmakuInput" placeholder="发送弹幕..." maxlength="50">
            <button id="sendDanmakuBtn">发送</button>
        </div>



        <script src="js/pako.min.js"></script>
        <script src="js/mp4box.all.min.js"></script>
        <script src="js/DHLiveMini.js"></script>
        <script src="js/MiniMateLoader.js"></script>
        <script src="js/MiniLive2.js"></script>
        <script src="js/simple_digital_human.js"></script>

        <!-- TensorFlow.js 和 BodyPix 库 -->
        <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/body-pix@2.2.1/dist/body-pix.min.js"></script>

        <!-- 高级背景抠图模块 -->
        <script src="js/advanced_green_screen.js"></script>
        <script src="js/transparent_background.js"></script>
        <script src="js/edge_processing.js"></script>
        <script src="js/background_replacement.js"></script>
        <script src="js/advanced_matting_controller.js"></script>

        <!-- 背景抠图集成模块 -->
        <script src="js/background_matting_integration.js"></script>
        <script src="js/advanced_matting_integration.js"></script>

        <!-- 调试工具（开发环境） -->
        <script src="js/matting_debugger.js"></script>
        <script src="js/matting_test.js"></script>

        <!-- 背景抠图功能脚本 -->
        <script>
            // 背景抠图功能实现
            class BackgroundMatting {
                constructor() {
                    this.enabled = false;
                    this.mode = 'ai'; // 'ai' 或 'greenscreen'
                    this.precision = 0.7;
                    this.featherRadius = 3;
                    this.backgroundType = 'color';
                    this.backgroundColor = '#00FF00';
                    this.backgroundImage = null;
                    this.backgroundVideo = null;

                    // BodyPix 模型
                    this.bodyPixModel = null;
                    this.isModelLoading = false;

                    // 性能监控
                    this.processingTimes = [];
                    this.lastProcessTime = 0;
                    this.targetFps = 15; // 目标帧率
                    this.adaptiveQuality = true; // 自适应质量
                    this.currentQuality = 'medium'; // 当前质量等级
                    this.skipFrameCount = 0; // 跳帧计数
                    this.maxSkipFrames = 2; // 最大跳帧数

                    // Canvas 元素
                    this.tempCanvas = document.createElement('canvas');
                    this.tempCtx = this.tempCanvas.getContext('2d');
                    this.maskCanvas = document.createElement('canvas');
                    this.maskCtx = this.maskCanvas.getContext('2d');

                    this.initializeUI();
                    this.loadBodyPixModel();
                }

                async loadBodyPixModel() {
                    if (this.isModelLoading || this.bodyPixModel) return;

                    this.isModelLoading = true;
                    document.getElementById('gpuStatus').textContent = '加载中...';

                    try {
                        // 检查 TensorFlow.js 是否可用
                        if (typeof tf === 'undefined' || typeof bodyPix === 'undefined') {
                            throw new Error('TensorFlow.js 或 BodyPix 库未加载');
                        }

                        // 设置 TensorFlow.js 后端
                        await tf.ready();
                        const backend = tf.getBackend();
                        console.log('TensorFlow.js backend:', backend);

                        // 加载 BodyPix 模型
                        this.bodyPixModel = await bodyPix.load({
                            architecture: 'MobileNetV1',
                            outputStride: 16,
                            multiplier: 0.75,
                            quantBytes: 2
                        });

                        document.getElementById('gpuStatus').textContent = backend === 'webgl' ? '已启用' : 'CPU模式';
                        console.log('BodyPix 模型加载成功');

                    } catch (error) {
                        console.error('BodyPix 模型加载失败:', error);
                        document.getElementById('gpuStatus').textContent = '加载失败';
                    } finally {
                        this.isModelLoading = false;
                    }
                }

                initializeUI() {
                    // 抠图开关
                    const mattingToggle = document.getElementById('mattingToggle');
                    mattingToggle.addEventListener('click', () => {
                        this.enabled = !this.enabled;
                        mattingToggle.classList.toggle('active', this.enabled);
                        console.log('背景抠图:', this.enabled ? '启用' : '禁用');
                    });

                    // 模式选择
                    const modeButtons = document.querySelectorAll('.mode-btn');
                    modeButtons.forEach(btn => {
                        btn.addEventListener('click', () => {
                            modeButtons.forEach(b => b.classList.remove('active'));
                            btn.classList.add('active');
                            this.mode = btn.dataset.mode;
                            console.log('抠图模式:', this.mode);
                        });
                    });

                    // 精度滑块
                    const precisionSlider = document.getElementById('precisionSlider');
                    const precisionValue = document.getElementById('precisionValue');
                    precisionSlider.addEventListener('input', (e) => {
                        this.precision = parseFloat(e.target.value);
                        precisionValue.textContent = this.precision.toFixed(1);
                    });

                    // 羽化滑块
                    const featherSlider = document.getElementById('featherSlider');
                    const featherValue = document.getElementById('featherValue');
                    featherSlider.addEventListener('input', (e) => {
                        this.featherRadius = parseInt(e.target.value);
                        featherValue.textContent = this.featherRadius;
                    });

                    // 背景类型选择
                    const bgTypeButtons = document.querySelectorAll('.bg-type-btn');
                    const uploadArea = document.getElementById('uploadArea');

                    bgTypeButtons.forEach(btn => {
                        btn.addEventListener('click', () => {
                            bgTypeButtons.forEach(b => b.classList.remove('active'));
                            btn.classList.add('active');
                            this.backgroundType = btn.dataset.type;

                            if (this.backgroundType === 'color') {
                                uploadArea.style.display = 'none';
                            } else {
                                uploadArea.style.display = 'block';
                            }
                        });
                    });

                    // 文件上传
                    const fileInput = document.getElementById('bgFileInput');
                    const uploadAreaElement = document.getElementById('uploadArea');

                    uploadAreaElement.addEventListener('click', () => fileInput.click());

                    uploadAreaElement.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        uploadAreaElement.classList.add('dragover');
                    });

                    uploadAreaElement.addEventListener('dragleave', () => {
                        uploadAreaElement.classList.remove('dragover');
                    });

                    uploadAreaElement.addEventListener('drop', (e) => {
                        e.preventDefault();
                        uploadAreaElement.classList.remove('dragover');
                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            this.handleFileUpload(files[0]);
                        }
                    });

                    fileInput.addEventListener('change', (e) => {
                        if (e.target.files.length > 0) {
                            this.handleFileUpload(e.target.files[0]);
                        }
                    });

                    // 预设模板按钮
                    const presetButtons = document.querySelectorAll('[data-preset]');
                    presetButtons.forEach(btn => {
                        btn.addEventListener('click', () => {
                            this.applyPreset(btn.dataset.preset);
                        });
                    });
                }

                // 应用预设模板
                applyPreset(presetName) {
                    const presets = {
                        portrait: {
                            mode: 'ai',
                            precision: 0.8,
                            featherRadius: 4,
                            backgroundType: 'color',
                            backgroundColor: '#F0F0F0',
                            description: '人像模式：高精度AI抠图，适合视频通话'
                        },
                        meeting: {
                            mode: 'ai',
                            precision: 0.6,
                            featherRadius: 2,
                            backgroundType: 'color',
                            backgroundColor: '#E8F4FD',
                            description: '会议模式：平衡性能和质量，适合在线会议'
                        },
                        gaming: {
                            mode: 'greenscreen',
                            precision: 0.7,
                            featherRadius: 1,
                            backgroundType: 'color',
                            backgroundColor: '#000000',
                            description: '游戏模式：绿幕抠图，低延迟高性能'
                        }
                    };

                    const preset = presets[presetName];
                    if (!preset) return;

                    // 应用预设参数
                    this.mode = preset.mode;
                    this.precision = preset.precision;
                    this.featherRadius = preset.featherRadius;
                    this.backgroundType = preset.backgroundType;
                    this.backgroundColor = preset.backgroundColor;

                    // 更新UI
                    this.updateUIFromSettings();

                    console.log(`应用预设: ${presetName} - ${preset.description}`);
                }

                // 从设置更新UI
                updateUIFromSettings() {
                    // 更新模式按钮
                    document.querySelectorAll('.mode-btn').forEach(btn => {
                        btn.classList.toggle('active', btn.dataset.mode === this.mode);
                    });

                    // 更新滑块
                    const precisionSlider = document.getElementById('precisionSlider');
                    const precisionValue = document.getElementById('precisionValue');
                    if (precisionSlider && precisionValue) {
                        precisionSlider.value = this.precision;
                        precisionValue.textContent = this.precision.toFixed(1);
                    }

                    const featherSlider = document.getElementById('featherSlider');
                    const featherValue = document.getElementById('featherValue');
                    if (featherSlider && featherValue) {
                        featherSlider.value = this.featherRadius;
                        featherValue.textContent = this.featherRadius;
                    }

                    // 更新背景类型
                    document.querySelectorAll('.bg-type-btn').forEach(btn => {
                        btn.classList.toggle('active', btn.dataset.type === this.backgroundType);
                    });
                }

                handleFileUpload(file) {
                    const fileType = file.type;

                    if (fileType.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const img = new Image();
                            img.onload = () => {
                                this.backgroundImage = img;
                                this.backgroundVideo = null;
                                console.log('背景图片加载成功');
                            };
                            img.src = e.target.result;
                        };
                        reader.readAsDataURL(file);

                    } else if (fileType.startsWith('video/')) {
                        const video = document.createElement('video');
                        video.muted = true;
                        video.loop = true;
                        video.autoplay = true;

                        const url = URL.createObjectURL(file);
                        video.src = url;

                        video.onloadeddata = () => {
                            this.backgroundVideo = video;
                            this.backgroundImage = null;
                            console.log('背景视频加载成功');
                        };

                    } else {
                        alert('不支持的文件格式，请选择图片或视频文件');
                    }
                }

                // RGB转HSV
                rgbToHsv(r, g, b) {
                    r /= 255;
                    g /= 255;
                    b /= 255;

                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    const diff = max - min;

                    let h = 0;
                    if (diff !== 0) {
                        if (max === r) {
                            h = ((g - b) / diff) % 6;
                        } else if (max === g) {
                            h = (b - r) / diff + 2;
                        } else {
                            h = (r - g) / diff + 4;
                        }
                    }
                    h = Math.round(h * 60);
                    if (h < 0) h += 360;

                    const s = max === 0 ? 0 : diff / max;
                    const v = max;

                    return [h, s * 100, v * 100];
                }

                // 绿幕抠图算法
                greenScreenMatting(imageData, width, height) {
                    const data = imageData.data;
                    const maskData = new Uint8ClampedArray(width * height);

                    // 绿色范围 (HSV)
                    const greenHueMin = 60;  // 绿色色相最小值
                    const greenHueMax = 180; // 绿色色相最大值
                    const saturationMin = 30; // 饱和度最小值
                    const valueMin = 30;     // 明度最小值

                    for (let i = 0; i < data.length; i += 4) {
                        const r = data[i];
                        const g = data[i + 1];
                        const b = data[i + 2];

                        const [h, s, v] = this.rgbToHsv(r, g, b);

                        // 判断是否为绿色
                        const isGreen = (h >= greenHueMin && h <= greenHueMax) &&
                                       (s >= saturationMin) &&
                                       (v >= valueMin);

                        const pixelIndex = Math.floor(i / 4);
                        maskData[pixelIndex] = isGreen ? 0 : 255; // 0=背景, 255=前景
                    }

                    return maskData;
                }

                // AI抠图
                async aiMatting(canvas, width, height) {
                    if (!this.bodyPixModel) {
                        console.warn('BodyPix 模型未加载');
                        return null;
                    }

                    try {
                        const segmentation = await this.bodyPixModel.segmentPerson(canvas, {
                            flipHorizontal: false,
                            internalResolution: 'medium',
                            segmentationThreshold: this.precision
                        });

                        return segmentation.data;
                    } catch (error) {
                        console.error('AI抠图失败:', error);
                        return null;
                    }
                }

                // 边缘检测
                detectEdges(maskData, width, height) {
                    const edges = new Uint8ClampedArray(width * height);

                    // Sobel算子
                    const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
                    const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];

                    for (let y = 1; y < height - 1; y++) {
                        for (let x = 1; x < width - 1; x++) {
                            let gx = 0, gy = 0;

                            for (let ky = -1; ky <= 1; ky++) {
                                for (let kx = -1; kx <= 1; kx++) {
                                    const idx = (y + ky) * width + (x + kx);
                                    const kernelIdx = (ky + 1) * 3 + (kx + 1);

                                    gx += maskData[idx] * sobelX[kernelIdx];
                                    gy += maskData[idx] * sobelY[kernelIdx];
                                }
                            }

                            const magnitude = Math.sqrt(gx * gx + gy * gy);
                            edges[y * width + x] = Math.min(255, magnitude);
                        }
                    }

                    return edges;
                }

                // 形态学操作：腐蚀
                erode(maskData, width, height, kernelSize = 3) {
                    const result = new Uint8ClampedArray(maskData.length);
                    const radius = Math.floor(kernelSize / 2);

                    for (let y = 0; y < height; y++) {
                        for (let x = 0; x < width; x++) {
                            let minValue = 255;

                            for (let ky = -radius; ky <= radius; ky++) {
                                for (let kx = -radius; kx <= radius; kx++) {
                                    const ny = Math.max(0, Math.min(height - 1, y + ky));
                                    const nx = Math.max(0, Math.min(width - 1, x + kx));
                                    minValue = Math.min(minValue, maskData[ny * width + nx]);
                                }
                            }

                            result[y * width + x] = minValue;
                        }
                    }

                    return result;
                }

                // 形态学操作：膨胀
                dilate(maskData, width, height, kernelSize = 3) {
                    const result = new Uint8ClampedArray(maskData.length);
                    const radius = Math.floor(kernelSize / 2);

                    for (let y = 0; y < height; y++) {
                        for (let x = 0; x < width; x++) {
                            let maxValue = 0;

                            for (let ky = -radius; ky <= radius; ky++) {
                                for (let kx = -radius; kx <= radius; kx++) {
                                    const ny = Math.max(0, Math.min(height - 1, y + ky));
                                    const nx = Math.max(0, Math.min(width - 1, x + kx));
                                    maxValue = Math.max(maxValue, maskData[ny * width + nx]);
                                }
                            }

                            result[y * width + x] = maxValue;
                        }
                    }

                    return result;
                }

                // 边缘羽化处理（增强版）
                applyFeathering(maskData, width, height, radius) {
                    if (radius <= 0) return maskData;

                    let result = new Uint8ClampedArray(maskData);

                    // 先进行形态学开运算（腐蚀后膨胀）来平滑边缘
                    if (radius > 2) {
                        result = this.erode(result, width, height, 3);
                        result = this.dilate(result, width, height, 3);
                    }

                    // 高斯模糊羽化
                    const kernelSize = radius * 2 + 1;
                    const kernel = [];

                    // 创建高斯核
                    const sigma = radius / 3;
                    let sum = 0;
                    for (let i = 0; i < kernelSize; i++) {
                        const x = i - radius;
                        const value = Math.exp(-(x * x) / (2 * sigma * sigma));
                        kernel.push(value);
                        sum += value;
                    }

                    // 归一化核
                    for (let i = 0; i < kernel.length; i++) {
                        kernel[i] /= sum;
                    }

                    // 水平模糊
                    const temp = new Float32Array(width * height);
                    for (let y = 0; y < height; y++) {
                        for (let x = 0; x < width; x++) {
                            let value = 0;
                            for (let k = 0; k < kernelSize; k++) {
                                const px = Math.max(0, Math.min(width - 1, x + k - radius));
                                value += result[y * width + px] * kernel[k];
                            }
                            temp[y * width + x] = value;
                        }
                    }

                    // 垂直模糊
                    const finalResult = new Uint8ClampedArray(width * height);
                    for (let y = 0; y < height; y++) {
                        for (let x = 0; x < width; x++) {
                            let value = 0;
                            for (let k = 0; k < kernelSize; k++) {
                                const py = Math.max(0, Math.min(height - 1, y + k - radius));
                                value += temp[py * width + x] * kernel[k];
                            }
                            finalResult[y * width + x] = Math.round(Math.max(0, Math.min(255, value)));
                        }
                    }

                    return finalResult;
                }

                // 背景合成
                compositeBackground(imageData, maskData, width, height) {
                    const data = imageData.data;

                    // 设置临时canvas尺寸
                    this.tempCanvas.width = width;
                    this.tempCanvas.height = height;

                    // 绘制背景
                    if (this.backgroundType === 'color') {
                        this.tempCtx.fillStyle = this.backgroundColor;
                        this.tempCtx.fillRect(0, 0, width, height);

                    } else if (this.backgroundType === 'image' && this.backgroundImage) {
                        this.tempCtx.drawImage(this.backgroundImage, 0, 0, width, height);

                    } else if (this.backgroundType === 'video' && this.backgroundVideo) {
                        this.tempCtx.drawImage(this.backgroundVideo, 0, 0, width, height);

                    } else {
                        // 默认背景
                        this.tempCtx.fillStyle = '#00FF00';
                        this.tempCtx.fillRect(0, 0, width, height);
                    }

                    const bgImageData = this.tempCtx.getImageData(0, 0, width, height);
                    const bgData = bgImageData.data;

                    // 合成前景和背景
                    for (let i = 0; i < data.length; i += 4) {
                        const pixelIndex = Math.floor(i / 4);
                        const alpha = maskData[pixelIndex] / 255;

                        // 线性插值混合
                        data[i] = Math.round(data[i] * alpha + bgData[i] * (1 - alpha));         // R
                        data[i + 1] = Math.round(data[i + 1] * alpha + bgData[i + 1] * (1 - alpha)); // G
                        data[i + 2] = Math.round(data[i + 2] * alpha + bgData[i + 2] * (1 - alpha)); // B
                        // Alpha通道保持不变
                    }

                    return imageData;
                }

                // 性能优化：检查是否应该跳过当前帧
                shouldSkipFrame() {
                    if (!this.adaptiveQuality) return false;

                    // 计算当前平均FPS
                    if (this.processingTimes.length < 5) return false;

                    const avgTime = this.processingTimes.slice(-5).reduce((a, b) => a + b, 0) / 5;
                    const currentFps = 1000 / avgTime;

                    // 如果FPS低于目标值，开始跳帧
                    if (currentFps < this.targetFps) {
                        this.skipFrameCount++;
                        if (this.skipFrameCount <= this.maxSkipFrames) {
                            return true;
                        }
                    }

                    this.skipFrameCount = 0;
                    return false;
                }

                // 自适应质量调整
                adaptQuality() {
                    if (!this.adaptiveQuality || this.processingTimes.length < 10) return;

                    const avgTime = this.processingTimes.slice(-10).reduce((a, b) => a + b, 0) / 10;
                    const currentFps = 1000 / avgTime;

                    if (currentFps < 10 && this.currentQuality !== 'low') {
                        this.currentQuality = 'low';
                        this.precision = Math.max(0.3, this.precision - 0.2);
                        this.featherRadius = Math.max(0, this.featherRadius - 2);
                        console.log('性能优化：降低到低质量模式');

                    } else if (currentFps < 15 && this.currentQuality === 'high') {
                        this.currentQuality = 'medium';
                        this.precision = Math.max(0.5, this.precision - 0.1);
                        this.featherRadius = Math.max(1, this.featherRadius - 1);
                        console.log('性能优化：降低到中等质量模式');

                    } else if (currentFps > 20 && this.currentQuality !== 'high') {
                        this.currentQuality = 'high';
                        this.precision = Math.min(0.9, this.precision + 0.1);
                        this.featherRadius = Math.min(5, this.featherRadius + 1);
                        console.log('性能优化：提升到高质量模式');
                    }
                }

                // 主要处理函数
                async processFrame(canvas, ctx) {
                    if (!this.enabled) return false;

                    // 性能优化：跳帧检查
                    if (this.shouldSkipFrame()) {
                        return false;
                    }

                    const startTime = performance.now();

                    try {
                        const width = canvas.width;
                        const height = canvas.height;

                        // 获取图像数据
                        const imageData = ctx.getImageData(0, 0, width, height);
                        let maskData = null;

                        // 根据模式进行抠图
                        if (this.mode === 'greenscreen') {
                            maskData = this.greenScreenMatting(imageData, width, height);
                        } else if (this.mode === 'ai') {
                            maskData = await this.aiMatting(canvas, width, height);
                        }

                        if (!maskData) return false;

                        // 应用边缘羽化（根据性能调整）
                        const effectiveFeatherRadius = this.currentQuality === 'low' ?
                            Math.max(0, this.featherRadius - 2) : this.featherRadius;

                        if (effectiveFeatherRadius > 0) {
                            maskData = this.applyFeathering(maskData, width, height, effectiveFeatherRadius);
                        }

                        // 合成背景
                        const resultImageData = this.compositeBackground(imageData, maskData, width, height);

                        // 将结果绘制回canvas
                        ctx.putImageData(resultImageData, 0, 0);

                        // 更新性能统计
                        const processingTime = performance.now() - startTime;
                        this.updatePerformanceStats(processingTime);

                        // 自适应质量调整
                        if (this.processingTimes.length % 30 === 0) {
                            this.adaptQuality();
                        }

                        return true;

                    } catch (error) {
                        console.error('背景抠图处理失败:', error);
                        return false;
                    }
                }

                // 更新性能统计
                updatePerformanceStats(processingTime) {
                    this.processingTimes.push(processingTime);

                    // 只保留最近30帧的数据
                    if (this.processingTimes.length > 30) {
                        this.processingTimes.shift();
                    }

                    // 计算平均处理时间和FPS
                    const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
                    const fps = Math.round(1000 / avgTime);

                    // 更新UI显示
                    const fpsElement = document.getElementById('processingFps');
                    if (fpsElement) {
                        fpsElement.textContent = fps;

                        // 根据性能设置颜色
                        if (fps >= 15) {
                            fpsElement.style.color = '#4cc9f0';
                        } else if (fps >= 10) {
                            fpsElement.style.color = '#ffa500';
                        } else {
                            fpsElement.style.color = '#ff4757';
                        }
                    }

                    // 更新质量等级显示
                    const qualityElement = document.getElementById('qualityLevel');
                    if (qualityElement) {
                        const qualityMap = {
                            'low': '低',
                            'medium': '中等',
                            'high': '高'
                        };
                        qualityElement.textContent = qualityMap[this.currentQuality] || '中等';

                        // 根据质量设置颜色
                        if (this.currentQuality === 'high') {
                            qualityElement.style.color = '#4cc9f0';
                        } else if (this.currentQuality === 'medium') {
                            qualityElement.style.color = '#ffa500';
                        } else {
                            qualityElement.style.color = '#ff4757';
                        }
                    }
                }

                // 获取当前设置
                getSettings() {
                    return {
                        enabled: this.enabled,
                        mode: this.mode,
                        precision: this.precision,
                        featherRadius: this.featherRadius,
                        backgroundType: this.backgroundType,
                        backgroundColor: this.backgroundColor
                    };
                }

                // 设置背景颜色
                setBackgroundColor(color) {
                    this.backgroundColor = color;
                }
            }

            // 兼容性检测
            function checkCompatibility() {
                const compatibility = {
                    webgl: false,
                    canvas: false,
                    tensorflow: false,
                    bodyPix: false,
                    fileAPI: false,
                    webWorkers: false
                };

                // 检查 WebGL 支持
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    compatibility.webgl = !!gl;
                } catch (e) {
                    compatibility.webgl = false;
                }

                // 检查 Canvas 支持
                try {
                    const canvas = document.createElement('canvas');
                    compatibility.canvas = !!(canvas.getContext && canvas.getContext('2d'));
                } catch (e) {
                    compatibility.canvas = false;
                }

                // 检查 TensorFlow.js
                compatibility.tensorflow = typeof tf !== 'undefined';

                // 检查 BodyPix
                compatibility.bodyPix = typeof bodyPix !== 'undefined';

                // 检查 File API
                compatibility.fileAPI = !!(window.File && window.FileReader && window.FileList && window.Blob);

                // 检查 Web Workers
                compatibility.webWorkers = typeof Worker !== 'undefined';

                return compatibility;
            }

            // 显示兼容性警告
            function showCompatibilityWarnings(compatibility) {
                const warnings = [];

                if (!compatibility.webgl) {
                    warnings.push('WebGL不支持，GPU加速功能将被禁用');
                }

                if (!compatibility.tensorflow || !compatibility.bodyPix) {
                    warnings.push('AI抠图库未加载，只能使用绿幕抠图模式');
                }

                if (!compatibility.fileAPI) {
                    warnings.push('文件API不支持，无法上传背景图片/视频');
                }

                if (warnings.length > 0) {
                    console.warn('背景抠图兼容性警告:', warnings);

                    // 可以在这里显示用户友好的警告信息
                    const gpuStatus = document.getElementById('gpuStatus');
                    if (gpuStatus && warnings.length > 0) {
                        gpuStatus.textContent = '部分功能受限';
                        gpuStatus.style.color = '#ffa500';
                    }
                }

                return warnings;
            }

            // 全局背景抠图实例
            let backgroundMatting = null;
            let advancedMattingController = null;

            // 调试函数
            function debugMattingSystem() {
                console.log('=== 高级背景抠图系统调试信息 ===');
                console.log('AdvancedGreenScreen:', typeof AdvancedGreenScreen);
                console.log('TransparentBackground:', typeof TransparentBackground);
                console.log('EdgeProcessing:', typeof EdgeProcessing);
                console.log('BackgroundReplacement:', typeof BackgroundReplacement);
                console.log('AdvancedMattingController:', typeof AdvancedMattingController);
                console.log('advancedMattingController instance:', advancedMattingController);

                const toggleElement = document.getElementById('advancedMattingToggle');
                console.log('Toggle element:', toggleElement);
                console.log('Toggle element classes:', toggleElement ? toggleElement.className : 'not found');

                if (advancedMattingController) {
                    console.log('Controller enabled:', advancedMattingController.enabled);
                    console.log('Controller settings:', advancedMattingController.getSettings());
                }
                console.log('=====================================');
            }

            // 延迟初始化函数
            function initializeAdvancedMatting() {
                return new Promise((resolve, reject) => {
                    let attempts = 0;
                    const maxAttempts = 50; // 5秒超时

                    const tryInit = () => {
                        attempts++;
                        console.log(`尝试初始化高级背景抠图 (${attempts}/${maxAttempts})`);

                        // 检查DOM元素是否存在
                        const toggleElement = document.getElementById('advancedMattingToggle');
                        if (!toggleElement) {
                            if (attempts < maxAttempts) {
                                setTimeout(tryInit, 100);
                                return;
                            } else {
                                reject(new Error('DOM元素未找到'));
                                return;
                            }
                        }

                        // 检查必要的类是否已加载
                        if (typeof AdvancedGreenScreen !== 'undefined' &&
                            typeof TransparentBackground !== 'undefined' &&
                            typeof EdgeProcessing !== 'undefined' &&
                            typeof BackgroundReplacement !== 'undefined' &&
                            typeof AdvancedMattingController !== 'undefined') {

                            try {
                                advancedMattingController = new AdvancedMattingController();

                                // 确保控制器正确暴露到全局作用域
                                window.advancedMattingController = advancedMattingController;

                                console.log('高级背景抠图功能初始化完成');

                                // 手动绑定事件（确保绑定成功）
                                setTimeout(() => {
                                    const toggle = document.getElementById('advancedMattingToggle');
                                    if (toggle && !toggle.hasAttribute('data-manual-event-bound')) {
                                        toggle.addEventListener('click', function() {
                                            console.log('抠图开关被点击 (手动绑定)');
                                            if (advancedMattingController) {
                                                advancedMattingController.enabled = !advancedMattingController.enabled;
                                                toggle.classList.toggle('active', advancedMattingController.enabled);
                                                console.log('抠图状态:', advancedMattingController.enabled ? '启用' : '禁用');

                                                // 触发状态变化回调
                                                if (advancedMattingController.onStateChange) {
                                                    advancedMattingController.onStateChange();
                                                }

                                                // 更新UI状态
                                                if (advancedMattingController.updateUI) {
                                                    advancedMattingController.updateUI();
                                                }

                                                // 启用/禁用集成处理
                                                if (window.advancedMattingIntegration) {
                                                    window.advancedMattingIntegration.setProcessingEnabled(advancedMattingController.enabled);
                                                }

                                                // 启用/禁用简化监控器
                                                if (window.simpleCanvasMonitor) {
                                                    window.simpleCanvasMonitor.setEnabled(advancedMattingController.enabled);
                                                }
                                            }
                                        });
                                        toggle.setAttribute('data-manual-event-bound', 'true');
                                        console.log('手动绑定抠图开关事件成功');
                                    }
                                }, 100);

                                // 设置性能监控
                                setInterval(() => {
                                    if (advancedMattingController && advancedMattingController.enabled) {
                                        const settings = advancedMattingController.getSettings();
                                        if (settings.performance && settings.performance.fps < 10) {
                                            console.log('性能警告: FPS过低，建议降低质量设置');
                                        }
                                    }
                                }, 5000);

                                resolve(advancedMattingController);

                            } catch (error) {
                                reject(error);
                            }

                        } else if (attempts < maxAttempts) {
                            setTimeout(tryInit, 100);
                        } else {
                            reject(new Error('高级背景抠图模块未完全加载'));
                        }
                    };

                    tryInit();
                });
            }

            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM加载完成，开始初始化背景抠图功能');

                // 检查兼容性
                const compatibility = checkCompatibility();
                const warnings = showCompatibilityWarnings(compatibility);

                // 延迟初始化高级背景抠图功能
                setTimeout(async () => {
                    try {
                        await initializeAdvancedMatting();

                        // 保持向后兼容
                        if (typeof BackgroundMatting !== 'undefined') {
                            backgroundMatting = new BackgroundMatting();
                            console.log('传统背景抠图功能也已初始化');
                        }

                        // 添加调试按钮（开发时使用）
                        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                            const debugBtn = document.createElement('button');
                            debugBtn.textContent = '调试抠图系统';
                            debugBtn.style.position = 'fixed';
                            debugBtn.style.top = '10px';
                            debugBtn.style.right = '10px';
                            debugBtn.style.zIndex = '9999';
                            debugBtn.style.padding = '5px 10px';
                            debugBtn.style.backgroundColor = '#ff4757';
                            debugBtn.style.color = 'white';
                            debugBtn.style.border = 'none';
                            debugBtn.style.borderRadius = '4px';
                            debugBtn.style.cursor = 'pointer';
                            debugBtn.onclick = debugMattingSystem;
                            document.body.appendChild(debugBtn);
                        }

                    } catch (error) {
                        console.error('背景抠图功能初始化失败:', error);

                        // 显示错误信息
                        const mattingToggle = document.getElementById('advancedMattingToggle');
                        if (mattingToggle) {
                            mattingToggle.style.opacity = '0.5';
                            mattingToggle.style.pointerEvents = 'none';
                            mattingToggle.title = '初始化失败: ' + error.message;
                        }

                        // 显示错误状态
                        const fpsElement = document.getElementById('advancedProcessingFps');
                        if (fpsElement) {
                            fpsElement.textContent = '错误';
                            fpsElement.style.color = '#ff4757';
                        }

                        // 在控制台显示详细错误信息
                        console.error('详细错误信息:', {
                            error: error,
                            stack: error.stack,
                            modules: {
                                AdvancedGreenScreen: typeof AdvancedGreenScreen,
                                TransparentBackground: typeof TransparentBackground,
                                EdgeProcessing: typeof EdgeProcessing,
                                BackgroundReplacement: typeof BackgroundReplacement,
                                AdvancedMattingController: typeof AdvancedMattingController
                            }
                        });
                    }
                }, 500); // 延迟500ms确保所有模块都已加载
            });
        </script>

        <!-- 背景色配置功能脚本 -->
        <script>
            // 背景色配置功能实现
            document.addEventListener('DOMContentLoaded', function() {
                const colorPicker = document.getElementById('colorPicker');
                const hexInput = document.getElementById('hexInput');
                const presetColorBtns = document.querySelectorAll('.preset-color-btn');
                const resetBgBtn = document.getElementById('resetBgBtn');
                const randomBgBtn = document.getElementById('randomBgBtn');
                const body = document.body;

                // 默认背景
                const defaultBackground = 'linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)';

                // 随机颜色数组
                const randomColors = [
                    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
                    '#FF8A80', '#80CBC4', '#81C784', '#FFB74D', '#F06292', '#BA68C8',
                    '#64B5F6', '#4DB6AC', '#AED581', '#FFD54F', '#FF8A65', '#A1887F'
                ];

                // 随机渐变色数组
                const randomGradients = [
                    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    'linear-gradient(45deg, #FF9A9E 0%, #FECFEF 50%, #FECFEF 100%)',
                    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                    'linear-gradient(135deg, #ff8a00 0%, #e52e71 100%)',
                    'linear-gradient(135deg, #667db6 0%, #0082c8 0%, #0082c8 0%, #667db6 100%)',
                    'linear-gradient(135deg, #f12711 0%, #f5af19 100%)'
                ];

                // 工具函数：将HEX转换为RGB
                function hexToRgb(hex) {
                    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                    return result ? {
                        r: parseInt(result[1], 16),
                        g: parseInt(result[2], 16),
                        b: parseInt(result[3], 16)
                    } : null;
                }

                // 工具函数：将RGB转换为HEX
                function rgbToHex(r, g, b) {
                    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                }

                // 工具函数：验证HEX颜色格式
                function isValidHex(hex) {
                    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
                }

                // 防抖函数
                function debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }

                // 设置背景色
                function setBackground(background) {
                    body.style.background = background;

                    // 如果是纯色，更新颜色选择器和HEX输入
                    if (background.startsWith('#')) {
                        colorPicker.value = background;
                        hexInput.value = background.toUpperCase();
                    } else if (background.startsWith('rgb')) {
                        // 处理RGB格式
                        const rgbMatch = background.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
                        if (rgbMatch) {
                            const hex = rgbToHex(parseInt(rgbMatch[1]), parseInt(rgbMatch[2]), parseInt(rgbMatch[3]));
                            colorPicker.value = hex;
                            hexInput.value = hex.toUpperCase();
                        }
                    } else {
                        // 渐变色情况，清空输入框
                        hexInput.value = '';
                        hexInput.placeholder = '渐变色';
                    }

                    // 更新预设按钮的激活状态
                    updatePresetButtonsState(background);
                }

                // 更新预设按钮的激活状态
                function updatePresetButtonsState(currentBackground) {
                    presetColorBtns.forEach(btn => {
                        btn.classList.remove('active');
                        if (btn.dataset.bg === currentBackground) {
                            btn.classList.add('active');
                        }
                    });
                }

                // 颜色选择器变化事件
                colorPicker.addEventListener('input', function() {
                    const color = this.value;
                    setBackground(color);
                });

                // HEX输入框变化事件（使用防抖优化性能）
                const debouncedHexInput = debounce(function(value, inputElement) {
                    if (isValidHex(value)) {
                        setBackground(value);
                        inputElement.style.borderColor = 'var(--primary-color)';
                    }
                }, 300);

                hexInput.addEventListener('input', function() {
                    let value = this.value.trim();

                    // 自动添加#号
                    if (value && !value.startsWith('#')) {
                        value = '#' + value;
                        this.value = value;
                    }

                    // 实时验证颜色格式
                    if (isValidHex(value)) {
                        this.style.borderColor = 'var(--primary-color)';
                        debouncedHexInput(value, this);
                    } else if (value.length > 1) {
                        this.style.borderColor = '#ff4757';
                    } else {
                        this.style.borderColor = 'var(--primary-color)';
                    }
                });

                // HEX输入框失去焦点时的处理
                hexInput.addEventListener('blur', function() {
                    this.style.borderColor = 'var(--primary-color)';
                    if (this.value && !isValidHex(this.value)) {
                        this.value = colorPicker.value.toUpperCase();
                    }
                });

                // 预设颜色按钮点击事件
                presetColorBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const background = this.dataset.bg;
                        setBackground(background);
                    });
                });

                // 重置按钮点击事件
                resetBgBtn.addEventListener('click', function() {
                    setBackground(defaultBackground);
                });

                // 随机背景按钮点击事件
                randomBgBtn.addEventListener('click', function() {
                    const useGradient = Math.random() > 0.5;
                    let randomBackground;

                    if (useGradient) {
                        randomBackground = randomGradients[Math.floor(Math.random() * randomGradients.length)];
                    } else {
                        randomBackground = randomColors[Math.floor(Math.random() * randomColors.length)];
                    }

                    setBackground(randomBackground);
                });

                // 初始化：设置默认激活状态
                updatePresetButtonsState(defaultBackground);

                console.log("背景色配置功能初始化完成");
            });
        </script>

        <!-- 弹幕功能脚本 -->
        <script>
            // 弹幕功能实现
            document.addEventListener('DOMContentLoaded', function() {
                const danmakuContainer = document.getElementById('danmakuContainer');
                const danmakuInput = document.getElementById('danmakuInput');
                const sendDanmakuBtn = document.getElementById('sendDanmakuBtn');
                
                // 随机颜色数组
                const colors = [
                    '#FFFFFF', // 白色
                    '#FF8A80', // 红色
                    '#FFFF8D', // 黄色
                    '#CCFF90', // 绿色
                    '#80D8FF', // 蓝色
                    '#B388FF', // 紫色
                    '#FFD180', // 橙色
                    '#F48FB1'  // 粉色
                ];
                
                // 发送弹幕函数
                function sendDanmaku(text) {
                    if (!text.trim()) return;
                    
                    // 创建弹幕元素
                    const danmaku = document.createElement('div');
                    danmaku.className = 'danmaku';
                    danmaku.textContent = text;
                    
                    // 随机设置弹幕样式
                    const fontSize = Math.floor(Math.random() * 16) + 20; // 20-36px
                    const color = colors[Math.floor(Math.random() * colors.length)];
                    const top = Math.floor(Math.random() * 80) + 5; // 5-85%
                    const duration = Math.floor(Math.random() * 5) + 8; // 8-13秒
                    
                    danmaku.style.fontSize = `${fontSize}px`;
                    danmaku.style.color = color;
                    danmaku.style.top = `${top}%`;
                    danmaku.style.animationDuration = `${duration}s`;
                    
                    // 添加到容器
                    danmakuContainer.appendChild(danmaku);
                    
                    // 动画结束后移除
                    setTimeout(() => {
                        danmaku.remove();
                    }, duration * 1000);
                }
                
                // 点击发送按钮
                sendDanmakuBtn.addEventListener('click', function() {
                    const text = danmakuInput.value;
                    sendDanmaku(text);
                    danmakuInput.value = '';
                });
                
                // 按回车发送
                danmakuInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const text = danmakuInput.value;
                        sendDanmaku(text);
                        danmakuInput.value = '';
                    }
                });
                
                // 监听对话框中的消息，自动生成弹幕
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'aiMessage') {
                        sendDanmaku(event.data.text);
                    }
                });
                
                // 初始隐藏弹幕输入框，等加载完成后显示
                const danmakuInputContainer = document.getElementById('danmakuInputContainer');
                danmakuInputContainer.style.display = 'none';
                
                // 监听加载完成事件
                const loadingCheck = setInterval(() => {
                    if (document.getElementById('loadingSpinner').style.display === 'none') {
                        danmakuInputContainer.style.display = 'flex';
                        clearInterval(loadingCheck);
                    }
                }, 1000);
            });
        </script>



        <!-- 口型动画控制脚本 -->
        <script>
            // 口型动画控制
            window.startMouthAnimation = function(duration) {
                console.log("主页面收到口型动画请求，持续时间:", duration);
                
                // 简单的口型动画序列
                const mouthSequence = [
                    [0.1, 0.1, 0.1, 0.1, 0.1, 0.1], // 闭合
                    [0.5, 0.3, 0.2, 0.1, 0.1, 0.1], // 半开
                    [0.8, 0.2, 0.1, 0.1, 0.1, 0.1], // 大开
                    [0.5, 0.3, 0.2, 0.1, 0.1, 0.1], // 半开
                    [0.2, 0.7, 0.3, 0.1, 0.1, 0.1], // 小开
                    [0.1, 0.1, 0.1, 0.1, 0.1, 0.1]  // 闭合
                ];
                
                // 动画帧率（每秒帧数）
                const fps = 10;
                // 计算总帧数
                const totalFrames = Math.floor(duration * fps);
                // 当前帧
                let currentFrame = 0;
                
                // 动画间隔ID
                let animationInterval = setInterval(() => {
                    // 计算当前应该显示的口型索引
                    const mouthIndex = currentFrame % mouthSequence.length;
                    
                    // 应用口型
                    if (window.Module && Module._setLipShape) {
                        try {
                            // 尝试直接设置口型
                            const lipShape = mouthSequence[mouthIndex];
                            Module._setLipShape(
                                lipShape[0], lipShape[1], lipShape[2], 
                                lipShape[3], lipShape[4], lipShape[5]
                            );
                        } catch (e) {
                            console.error("设置口型失败:", e);
                        }
                    }
                    
                    // 增加帧计数
                    currentFrame++;
                    
                    // 如果动画结束，清除间隔
                    if (currentFrame >= totalFrames) {
                        clearInterval(animationInterval);
                        
                        // 重置口型
                        if (window.Module && Module._setLipShape) {
                            try {
                                Module._setLipShape(0, 0, 0, 0, 0, 0);
                            } catch (e) {
                                console.error("重置口型失败:", e);
                            }
                        }
                    }
                }, 1000 / fps);
            };
        </script>
    </body>
</html>
