<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景色配置功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);
            transition: background 0.5s ease;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4361ee;
        }
        
        .color-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .color-picker {
            width: 50px;
            height: 40px;
            border: 2px solid #4361ee;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .hex-input {
            padding: 10px;
            border: 2px solid #4361ee;
            border-radius: 8px;
            font-size: 16px;
            width: 120px;
        }
        
        .preset-colors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        
        .preset-btn {
            width: 40px;
            height: 40px;
            border: 2px solid #fff;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .preset-btn:hover {
            transform: scale(1.1);
        }
        
        .control-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin-right: 10px;
            transition: all 0.3s;
        }
        
        .reset-btn {
            background: #4361ee;
            color: white;
        }
        
        .random-btn {
            background: #4cc9f0;
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
        }
        
        .test-result {
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .digital-human-placeholder {
            width: 200px;
            height: 300px;
            background: rgba(255, 255, 255, 0.8);
            border: 3px solid #4361ee;
            border-radius: 15px;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #4361ee;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 数字人背景色配置功能测试</h1>
        
        <div class="test-section">
            <h3>1. 颜色选择器测试</h3>
            <div class="color-controls">
                <input type="color" id="colorPicker" class="color-picker" value="#8EC5FC">
                <input type="text" id="hexInput" class="hex-input" placeholder="#8EC5FC" maxlength="7">
                <button class="control-btn reset-btn" onclick="resetBackground()">重置</button>
                <button class="control-btn random-btn" onclick="randomBackground()">随机</button>
            </div>
            <div class="test-result">
                <strong>测试结果：</strong>颜色选择器和HEX输入框应该能够实时改变背景色
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 预设颜色测试</h3>
            <div class="preset-colors">
                <div class="preset-btn" style="background: linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%);" onclick="setBackground('linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)')"></div>
                <div class="preset-btn" style="background: #FF6B6B;" onclick="setBackground('#FF6B6B')"></div>
                <div class="preset-btn" style="background: #4ECDC4;" onclick="setBackground('#4ECDC4')"></div>
                <div class="preset-btn" style="background: #45B7D1;" onclick="setBackground('#45B7D1')"></div>
                <div class="preset-btn" style="background: #96CEB4;" onclick="setBackground('#96CEB4')"></div>
                <div class="preset-btn" style="background: #FFEAA7;" onclick="setBackground('#FFEAA7')"></div>
                <div class="preset-btn" style="background: #DDA0DD;" onclick="setBackground('#DDA0DD')"></div>
                <div class="preset-btn" style="background: linear-gradient(45deg, #FF9A9E 0%, #FECFEF 100%);" onclick="setBackground('linear-gradient(45deg, #FF9A9E 0%, #FECFEF 100%)')"></div>
            </div>
            <div class="test-result">
                <strong>测试结果：</strong>点击预设颜色按钮应该立即改变背景色
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 数字人显示效果测试</h3>
            <div class="digital-human-placeholder">
                数字人显示区域
                <br>
                (模拟)
            </div>
            <div class="test-result">
                <strong>测试结果：</strong>背景色变化不应影响数字人的显示效果，边框和内容应保持清晰可见
            </div>
        </div>
        
        <div class="test-section">
            <h3>4. 性能测试</h3>
            <button class="control-btn" onclick="performanceTest()">开始性能测试</button>
            <div id="performanceResult" class="test-result" style="display: none;">
                <strong>性能测试结果：</strong><span id="performanceText"></span>
            </div>
        </div>
    </div>

    <script>
        const body = document.body;
        const colorPicker = document.getElementById('colorPicker');
        const hexInput = document.getElementById('hexInput');
        const defaultBackground = 'linear-gradient(135deg, #8EC5FC 0%, #E0C3FC 100%)';
        
        // 随机颜色数组
        const randomColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
            '#FF8A80', '#80CBC4', '#81C784', '#FFB74D', '#F06292', '#BA68C8'
        ];
        
        const randomGradients = [
            'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
        ];
        
        function setBackground(background) {
            body.style.background = background;
            
            if (background.startsWith('#')) {
                colorPicker.value = background;
                hexInput.value = background.toUpperCase();
            } else {
                hexInput.value = '';
                hexInput.placeholder = '渐变色';
            }
        }
        
        function resetBackground() {
            setBackground(defaultBackground);
        }
        
        function randomBackground() {
            const useGradient = Math.random() > 0.5;
            let randomBg;
            
            if (useGradient) {
                randomBg = randomGradients[Math.floor(Math.random() * randomGradients.length)];
            } else {
                randomBg = randomColors[Math.floor(Math.random() * randomColors.length)];
            }
            
            setBackground(randomBg);
        }
        
        function isValidHex(hex) {
            return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
        }
        
        function performanceTest() {
            const startTime = performance.now();
            const testCount = 100;
            
            for (let i = 0; i < testCount; i++) {
                const randomColor = randomColors[Math.floor(Math.random() * randomColors.length)];
                setBackground(randomColor);
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            document.getElementById('performanceResult').style.display = 'block';
            document.getElementById('performanceText').textContent = 
                `完成${testCount}次背景色切换，耗时：${duration.toFixed(2)}ms，平均每次：${(duration/testCount).toFixed(2)}ms`;
        }
        
        // 事件监听器
        colorPicker.addEventListener('input', function() {
            setBackground(this.value);
        });
        
        hexInput.addEventListener('input', function() {
            let value = this.value.trim();
            
            if (value && !value.startsWith('#')) {
                value = '#' + value;
                this.value = value;
            }
            
            if (isValidHex(value)) {
                setBackground(value);
                this.style.borderColor = '#4361ee';
            } else if (value.length > 1) {
                this.style.borderColor = '#ff4757';
            } else {
                this.style.borderColor = '#4361ee';
            }
        });
        
        console.log('背景色配置测试页面加载完成');
    </script>
</body>
</html>
