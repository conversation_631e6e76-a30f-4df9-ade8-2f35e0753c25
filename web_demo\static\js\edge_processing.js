/**
 * 边缘处理模块
 * 实现高级边缘羽化和抗锯齿处理算法
 */

class EdgeProcessing {
    constructor() {
        // 边缘处理参数
        this.featherRadius = 3;           // 羽化半径
        this.antiAliasStrength = 0.5;     // 抗锯齿强度
        this.edgeThreshold = 0.1;         // 边缘检测阈值
        this.smoothingIterations = 2;     // 平滑迭代次数
        
        // 高级参数
        this.enableGradientFeather = true;  // 启用梯度羽化
        this.enableEdgeDetection = true;    // 启用边缘检测
        this.enableSubpixelSmoothing = true; // 启用亚像素平滑
        
        // 工作缓冲区
        this.edgeBuffer = null;
        this.gradientBuffer = null;
        this.tempBuffer = null;
        
        // 性能监控
        this.processingTimes = [];
    }
    
    /**
     * 主要边缘处理方法
     * @param {Float32Array} maskData - 输入遮罩数据
     * @param {number} width - 图像宽度
     * @param {number} height - 图像高度
     * @returns {Float32Array} 处理后的遮罩数据
     */
    processEdges(maskData, width, height) {
        const startTime = performance.now();
        
        // 初始化缓冲区
        this.initBuffers(width, height);
        
        let result = new Float32Array(maskData);
        
        // 第一步：边缘检测
        if (this.enableEdgeDetection) {
            const edges = this.detectEdges(result, width, height);
            this.edgeBuffer.set(edges);
        }
        
        // 第二步：梯度羽化
        if (this.enableGradientFeather && this.featherRadius > 0) {
            result = this.gradientFeather(result, width, height);
        }
        
        // 第三步：抗锯齿处理
        if (this.antiAliasStrength > 0) {
            result = this.antiAlias(result, width, height);
        }
        
        // 第四步：亚像素平滑
        if (this.enableSubpixelSmoothing) {
            result = this.subpixelSmoothing(result, width, height);
        }
        
        // 第五步：多次平滑迭代
        for (let i = 0; i < this.smoothingIterations; i++) {
            result = this.bilateralFilter(result, width, height);
        }
        
        // 性能统计
        const processingTime = performance.now() - startTime;
        this.processingTimes.push(processingTime);
        if (this.processingTimes.length > 30) {
            this.processingTimes.shift();
        }
        
        return result;
    }
    
    /**
     * 初始化工作缓冲区
     */
    initBuffers(width, height) {
        const size = width * height;
        if (!this.edgeBuffer || this.edgeBuffer.length !== size) {
            this.edgeBuffer = new Float32Array(size);
            this.gradientBuffer = new Float32Array(size);
            this.tempBuffer = new Float32Array(size);
        }
    }
    
    /**
     * 边缘检测 - 使用Sobel算子
     */
    detectEdges(maskData, width, height) {
        const edges = new Float32Array(width * height);
        
        // Sobel算子
        const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
        const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                let gx = 0, gy = 0;
                
                // 应用Sobel算子
                for (let ky = -1; ky <= 1; ky++) {
                    for (let kx = -1; kx <= 1; kx++) {
                        const idx = (y + ky) * width + (x + kx);
                        const kernelIdx = (ky + 1) * 3 + (kx + 1);
                        
                        gx += maskData[idx] * sobelX[kernelIdx];
                        gy += maskData[idx] * sobelY[kernelIdx];
                    }
                }
                
                // 计算梯度幅值
                const magnitude = Math.sqrt(gx * gx + gy * gy);
                edges[y * width + x] = Math.min(1, magnitude);
            }
        }
        
        return edges;
    }
    
    /**
     * 梯度羽化 - 基于距离变换的羽化
     */
    gradientFeather(maskData, width, height) {
        const result = new Float32Array(maskData);
        const radius = this.featherRadius;
        
        if (radius <= 0) return result;
        
        // 计算距离变换
        const distanceMap = this.computeDistanceTransform(maskData, width, height);
        
        // 应用羽化
        for (let i = 0; i < result.length; i++) {
            const distance = distanceMap[i];
            
            if (distance < radius) {
                // 在羽化区域内，使用平滑过渡
                const factor = distance / radius;
                const smoothFactor = this.smoothStep(factor);
                result[i] = maskData[i] * smoothFactor;
            }
        }
        
        return result;
    }
    
    /**
     * 计算距离变换
     */
    computeDistanceTransform(maskData, width, height) {
        const distances = new Float32Array(width * height);
        const maxDistance = Math.sqrt(width * width + height * height);
        
        // 初始化距离
        for (let i = 0; i < distances.length; i++) {
            distances[i] = maskData[i] > 0.5 ? maxDistance : 0;
        }
        
        // 前向扫描
        for (let y = 1; y < height; y++) {
            for (let x = 1; x < width; x++) {
                const idx = y * width + x;
                if (maskData[idx] > 0.5) {
                    const d1 = distances[(y - 1) * width + x] + 1;
                    const d2 = distances[y * width + (x - 1)] + 1;
                    const d3 = distances[(y - 1) * width + (x - 1)] + Math.SQRT2;
                    distances[idx] = Math.min(distances[idx], d1, d2, d3);
                }
            }
        }
        
        // 后向扫描
        for (let y = height - 2; y >= 0; y--) {
            for (let x = width - 2; x >= 0; x--) {
                const idx = y * width + x;
                if (maskData[idx] > 0.5) {
                    const d1 = distances[(y + 1) * width + x] + 1;
                    const d2 = distances[y * width + (x + 1)] + 1;
                    const d3 = distances[(y + 1) * width + (x + 1)] + Math.SQRT2;
                    distances[idx] = Math.min(distances[idx], d1, d2, d3);
                }
            }
        }
        
        return distances;
    }
    
    /**
     * 平滑步进函数
     */
    smoothStep(t) {
        t = Math.max(0, Math.min(1, t));
        return t * t * (3 - 2 * t);
    }
    
    /**
     * 抗锯齿处理 - 使用超采样
     */
    antiAlias(maskData, width, height) {
        const result = new Float32Array(maskData);
        const strength = this.antiAliasStrength;
        
        if (strength <= 0) return result;
        
        // 对每个像素进行超采样
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const idx = y * width + x;
                
                // 检查是否在边缘附近
                if (this.isNearEdge(maskData, x, y, width, height)) {
                    // 进行2x2超采样
                    const samples = this.supersample2x2(maskData, x, y, width, height);
                    const avgSample = samples.reduce((a, b) => a + b, 0) / samples.length;
                    
                    // 混合原始值和超采样值
                    result[idx] = maskData[idx] * (1 - strength) + avgSample * strength;
                }
            }
        }
        
        return result;
    }
    
    /**
     * 检查像素是否在边缘附近
     */
    isNearEdge(maskData, x, y, width, height) {
        const center = maskData[y * width + x];
        const threshold = this.edgeThreshold;
        
        // 检查8邻域
        for (let dy = -1; dy <= 1; dy++) {
            for (let dx = -1; dx <= 1; dx++) {
                if (dx === 0 && dy === 0) continue;
                
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const neighbor = maskData[ny * width + nx];
                    if (Math.abs(center - neighbor) > threshold) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * 2x2超采样
     */
    supersample2x2(maskData, x, y, width, height) {
        const samples = [];
        
        // 在像素内部进行4个子采样
        const offsets = [
            [-0.25, -0.25], [0.25, -0.25],
            [-0.25, 0.25], [0.25, 0.25]
        ];
        
        for (const [dx, dy] of offsets) {
            const sx = x + dx;
            const sy = y + dy;
            const sample = this.bilinearSample(maskData, sx, sy, width, height);
            samples.push(sample);
        }
        
        return samples;
    }
    
    /**
     * 双线性采样
     */
    bilinearSample(maskData, x, y, width, height) {
        const x1 = Math.floor(x);
        const y1 = Math.floor(y);
        const x2 = Math.min(x1 + 1, width - 1);
        const y2 = Math.min(y1 + 1, height - 1);
        
        const fx = x - x1;
        const fy = y - y1;
        
        const v11 = maskData[y1 * width + x1];
        const v12 = maskData[y1 * width + x2];
        const v21 = maskData[y2 * width + x1];
        const v22 = maskData[y2 * width + x2];
        
        const v1 = v11 * (1 - fx) + v12 * fx;
        const v2 = v21 * (1 - fx) + v22 * fx;
        
        return v1 * (1 - fy) + v2 * fy;
    }
    
    /**
     * 亚像素平滑
     */
    subpixelSmoothing(maskData, width, height) {
        const result = new Float32Array(maskData);
        
        // 使用Lanczos重采样进行亚像素平滑
        for (let y = 2; y < height - 2; y++) {
            for (let x = 2; x < width - 2; x++) {
                const idx = y * width + x;
                
                if (this.isNearEdge(maskData, x, y, width, height)) {
                    result[idx] = this.lanczosResample(maskData, x, y, width, height);
                }
            }
        }
        
        return result;
    }
    
    /**
     * Lanczos重采样
     */
    lanczosResample(maskData, x, y, width, height) {
        let sum = 0;
        let weightSum = 0;
        const a = 2; // Lanczos参数
        
        for (let dy = -a + 1; dy < a; dy++) {
            for (let dx = -a + 1; dx < a; dx++) {
                const nx = x + dx;
                const ny = y + dy;
                
                if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const weight = this.lanczosKernel(distance, a);
                    
                    sum += maskData[ny * width + nx] * weight;
                    weightSum += weight;
                }
            }
        }
        
        return weightSum > 0 ? sum / weightSum : maskData[y * width + x];
    }
    
    /**
     * Lanczos核函数
     */
    lanczosKernel(x, a) {
        if (x === 0) return 1;
        if (Math.abs(x) >= a) return 0;
        
        const piX = Math.PI * x;
        const piXa = piX / a;
        
        return (Math.sin(piX) / piX) * (Math.sin(piXa) / piXa);
    }
    
    /**
     * 双边滤波
     */
    bilateralFilter(maskData, width, height) {
        const result = new Float32Array(maskData);
        const spatialSigma = 2.0;
        const intensitySigma = 0.1;
        const kernelRadius = 3;
        
        for (let y = kernelRadius; y < height - kernelRadius; y++) {
            for (let x = kernelRadius; x < width - kernelRadius; x++) {
                const centerIdx = y * width + x;
                const centerValue = maskData[centerIdx];
                
                let sum = 0;
                let weightSum = 0;
                
                for (let dy = -kernelRadius; dy <= kernelRadius; dy++) {
                    for (let dx = -kernelRadius; dx <= kernelRadius; dx++) {
                        const nx = x + dx;
                        const ny = y + dy;
                        const idx = ny * width + nx;
                        const value = maskData[idx];
                        
                        // 空间权重
                        const spatialDist = Math.sqrt(dx * dx + dy * dy);
                        const spatialWeight = Math.exp(-(spatialDist * spatialDist) / (2 * spatialSigma * spatialSigma));
                        
                        // 强度权重
                        const intensityDist = Math.abs(value - centerValue);
                        const intensityWeight = Math.exp(-(intensityDist * intensityDist) / (2 * intensitySigma * intensitySigma));
                        
                        const weight = spatialWeight * intensityWeight;
                        sum += value * weight;
                        weightSum += weight;
                    }
                }
                
                result[centerIdx] = weightSum > 0 ? sum / weightSum : centerValue;
            }
        }
        
        return result;
    }
    
    /**
     * 设置边缘处理参数
     */
    setParameters(params) {
        if (params.featherRadius !== undefined) this.featherRadius = params.featherRadius;
        if (params.antiAliasStrength !== undefined) this.antiAliasStrength = params.antiAliasStrength;
        if (params.edgeThreshold !== undefined) this.edgeThreshold = params.edgeThreshold;
        if (params.smoothingIterations !== undefined) this.smoothingIterations = params.smoothingIterations;
        if (params.enableGradientFeather !== undefined) this.enableGradientFeather = params.enableGradientFeather;
        if (params.enableEdgeDetection !== undefined) this.enableEdgeDetection = params.enableEdgeDetection;
        if (params.enableSubpixelSmoothing !== undefined) this.enableSubpixelSmoothing = params.enableSubpixelSmoothing;
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (this.processingTimes.length === 0) {
            return { avgTime: 0, fps: 0 };
        }
        
        const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
        const fps = Math.round(1000 / avgTime);
        
        return {
            avgTime: avgTime.toFixed(2),
            fps: fps
        };
    }
}

// 导出模块
window.EdgeProcessing = EdgeProcessing;
