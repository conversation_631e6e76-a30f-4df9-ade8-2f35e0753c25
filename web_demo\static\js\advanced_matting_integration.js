/**
 * 高级背景抠图集成模块
 * 将高级背景抠图功能集成到现有的数字人视频处理流程中
 */

class AdvancedMattingIntegration {
    constructor() {
        this.controller = null;
        this.originalProcessVideoFrames = null;
        this.originalProcessDataSet = null;
        this.isIntegrated = false;
        this.processingEnabled = false;
        
        // 性能监控
        this.frameSkipCount = 0;
        this.maxFrameSkip = 2;
        this.lastProcessTime = 0;
        this.targetFrameTime = 1000 / 15; // 15 FPS target
        
        // 错误处理
        this.errorCount = 0;
        this.maxErrors = 5;
        this.lastErrorTime = 0;
        
        console.log('高级背景抠图集成模块初始化');
    }
    
    /**
     * 初始化集成
     */
    async initialize(mattingController) {
        try {
            this.controller = mattingController;
            
            // 等待原始函数加载
            await this.waitForOriginalFunctions();
            
            // 备份原始函数
            this.backupOriginalFunctions();
            
            // 替换为增强版本
            this.replaceWithEnhancedFunctions();
            
            this.isIntegrated = true;
            this.processingEnabled = true;
            
            console.log('高级背景抠图集成完成');
            
        } catch (error) {
            console.error('高级背景抠图集成失败:', error);
            this.handleError(error);
        }
    }
    
    /**
     * 等待原始函数加载
     */
    async waitForOriginalFunctions() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 100; // 10秒超时

            const checkFunctions = () => {
                attempts++;

                // 检查必要的全局变量和函数
                const hasProcessVideoFrames = typeof processVideoFrames !== 'undefined';
                const hasProcessDataSet = typeof processDataSet !== 'undefined';
                const hasCanvasVideo = typeof canvas_video !== 'undefined' && canvas_video;
                const hasCtxVideo = typeof ctx_video !== 'undefined' && ctx_video;

                console.log(`等待原始函数加载 (${attempts}/${maxAttempts}):`, {
                    processVideoFrames: hasProcessVideoFrames,
                    processDataSet: hasProcessDataSet,
                    canvas_video: hasCanvasVideo,
                    ctx_video: hasCtxVideo
                });

                if (hasProcessVideoFrames && hasProcessDataSet && hasCanvasVideo && hasCtxVideo) {
                    console.log('所有必要的函数和变量已加载');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.warn('部分函数未加载，但继续初始化');
                    resolve(); // 改为resolve而不是reject，允许部分功能工作
                } else {
                    setTimeout(checkFunctions, 100);
                }
            };

            checkFunctions();
        });
    }
    
    /**
     * 备份原始函数
     */
    backupOriginalFunctions() {
        if (typeof processVideoFrames !== 'undefined') {
            this.originalProcessVideoFrames = processVideoFrames;
        }
        if (typeof processDataSet !== 'undefined') {
            this.originalProcessDataSet = processDataSet;
        }
    }
    
    /**
     * 替换为增强版本
     */
    replaceWithEnhancedFunctions() {
        const self = this;
        
        // 增强的视频帧处理函数
        window.processVideoFrames = async function() {
            if (isPaused) {
                return;
            }
            
            try {
                // 检查视频帧是否已经解码完成
                if (videoProcessor.videoFrames.length === 0 || 
                    videoProcessor.videoFrames.length < videoProcessor.nbSampleTotal) {
                    console.log('Waiting for video frames to load...', 
                               videoProcessor.videoFrames.length, videoProcessor.nbSampleTotal);
                    setTimeout(processVideoFrames, 100);
                    return;
                }
                
                if (frameIndex >= videoProcessor.videoFrames.length) {
                    frameIndex = 0;
                }
                
                const startTime = performance.now();
                
                // 绘制视频帧
                if (tag_ios17) {
                    const { blob, duration, timestamp } = videoProcessor.videoFrames[frameIndex];
                    const img = await createImageBitmap(blob);
                    ctx_video.drawImage(img, 0, 0, canvas_video.width, canvas_video.height);
                    img.close();
                } else {
                    const { img, duration, timestamp } = videoProcessor.videoFrames[frameIndex];
                    ctx_video.drawImage(img, 0, 0, canvas_video.width, canvas_video.height);
                }
                
                // 应用高级背景抠图处理
                if (self.shouldProcessFrame()) {
                    await self.processAdvancedMatting();
                }
                
                // 计算并显示FPS
                if (fps_enabled) {
                    const currentTime = performance.now();
                    const deltaTime = currentTime - lastFrameTime;
                    frameTimes.push(currentTime);
                    
                    while (frameTimes.length > 0 && currentTime - frameTimes[0] > 1000) {
                        frameTimes.shift();
                    }
                    
                    const fps = frameTimes.length;
                    ctx_video.fillStyle = 'white';
                    ctx_video.font = '16px Arial';
                    ctx_video.textAlign = 'right';
                    ctx_video.fillText(`FPS: ${fps}`, canvas_video.width - 10, 20);
                }
                
                processDataSet(frameIndex);
                frameIndex++;
                
                // 动态调整帧率
                const processingTime = performance.now() - startTime;
                const delay = Math.max(0, self.targetFrameTime - processingTime);
                self.lastProcessTime = processingTime;
                
                setTimeout(processVideoFrames, delay);
                
            } catch (error) {
                console.error('视频帧处理错误:', error);
                self.handleError(error);
                
                // 继续处理下一帧
                setTimeout(processVideoFrames, 100);
            }
        };
        
        // 保持原始的数据集处理函数
        window.processDataSet = this.originalProcessDataSet;
    }
    
    /**
     * 判断是否应该处理当前帧
     */
    shouldProcessFrame() {
        // 基础状态检查
        if (!this.processingEnabled) {
            return false;
        }

        if (!this.controller) {
            console.warn('⚠️ 抠图控制器未设置');
            return false;
        }

        if (!this.controller.enabled) {
            return false;
        }

        // 检查必要的模块是否存在
        if (!this.controller.greenScreen || !this.controller.edgeProcessor) {
            console.warn('⚠️ 抠图处理模块未完全加载');
            return false;
        }

        // 性能优化：跳帧处理
        if (this.lastProcessTime > this.targetFrameTime * 1.5) {
            this.frameSkipCount++;
            if (this.frameSkipCount <= this.maxFrameSkip) {
                return false;
            }
        }

        this.frameSkipCount = 0;
        return true;
    }
    
    /**
     * 处理高级背景抠图
     */
    async processAdvancedMatting() {
        try {
            // 检查必要的全局变量
            if (typeof canvas_video === 'undefined' || !canvas_video ||
                typeof ctx_video === 'undefined' || !ctx_video) {
                console.warn('canvas_video 或 ctx_video 未定义，跳过抠图处理');
                return;
            }

            const width = canvas_video.width;
            const height = canvas_video.height;

            if (width <= 0 || height <= 0) {
                console.warn('画布尺寸无效，跳过抠图处理');
                return;
            }

            // 获取当前帧的图像数据
            const imageData = ctx_video.getImageData(0, 0, width, height);

            // 检查图像数据是否有效
            if (!imageData || !imageData.data || imageData.data.length === 0) {
                console.warn('图像数据无效，跳过抠图处理');
                return;
            }

            // 应用高级背景抠图处理
            const processedImageData = await this.controller.process(imageData, width, height);

            // 检查处理结果
            if (processedImageData && processedImageData.data) {
                // 将处理后的图像绘制回canvas
                ctx_video.putImageData(processedImageData, 0, 0);

                // 重置错误计数
                this.errorCount = 0;

                console.log('抠图处理成功');
            } else {
                console.warn('抠图处理返回无效数据');
            }

        } catch (error) {
            console.error('高级背景抠图处理错误:', error);
            this.handleError(error);
        }
    }
    
    /**
     * 错误处理
     */
    handleError(error) {
        this.errorCount++;
        this.lastErrorTime = performance.now();
        
        // 如果错误过多，暂时禁用处理
        if (this.errorCount >= this.maxErrors) {
            console.warn('错误过多，暂时禁用高级背景抠图处理');
            this.processingEnabled = false;
            
            // 5秒后重新启用
            setTimeout(() => {
                this.errorCount = 0;
                this.processingEnabled = true;
                console.log('重新启用高级背景抠图处理');
            }, 5000);
        }
        
        // 更新UI显示错误状态
        this.updateErrorStatus();
    }
    
    /**
     * 更新错误状态显示
     */
    updateErrorStatus() {
        const fpsElement = document.getElementById('advancedProcessingFps');
        if (fpsElement && !this.processingEnabled) {
            fpsElement.textContent = '错误';
            fpsElement.style.color = '#ff4757';
        }
    }
    
    /**
     * 恢复原始函数
     */
    restore() {
        try {
            if (this.originalProcessVideoFrames) {
                window.processVideoFrames = this.originalProcessVideoFrames;
            }
            if (this.originalProcessDataSet) {
                window.processDataSet = this.originalProcessDataSet;
            }
            
            this.isIntegrated = false;
            this.processingEnabled = false;
            
            console.log('高级背景抠图集成已恢复');
            
        } catch (error) {
            console.error('恢复原始函数失败:', error);
        }
    }
    
    /**
     * 检查集成状态
     */
    isReady() {
        return this.isIntegrated && this.controller && this.processingEnabled;
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (!this.controller) {
            return null;
        }
        
        const settings = this.controller.getSettings();
        return {
            enabled: this.controller.enabled,
            backgroundType: this.controller.currentBackgroundType,
            quality: this.controller.currentQuality,
            fps: settings.performance.fps,
            frameCount: settings.performance.frameCount,
            errorCount: this.errorCount,
            processingEnabled: this.processingEnabled,
            lastProcessTime: this.lastProcessTime
        };
    }
    
    /**
     * 设置处理参数
     */
    setProcessingParams(params) {
        if (params.targetFps) {
            this.targetFrameTime = 1000 / params.targetFps;
        }
        if (params.maxFrameSkip !== undefined) {
            this.maxFrameSkip = params.maxFrameSkip;
        }
        if (params.maxErrors !== undefined) {
            this.maxErrors = params.maxErrors;
        }
    }
    
    /**
     * 手动启用/禁用处理
     */
    setProcessingEnabled(enabled) {
        this.processingEnabled = enabled;
        if (enabled) {
            this.errorCount = 0;
        }
        console.log('高级背景抠图处理:', enabled ? '启用' : '禁用');
    }
}

// 全局集成实例
let advancedMattingIntegration = null;

// 简化的画布监控器
class SimpleCanvasMonitor {
    constructor() {
        this.controller = null;
        this.isProcessing = false;
        this.lastProcessTime = 0;
        this.targetInterval = 1000 / 15; // 15 FPS
        this.enabled = false;

        console.log('简化画布监控器初始化');
    }

    setController(controller) {
        this.controller = controller;
        console.log('设置抠图控制器');
    }

    setEnabled(enabled) {
        this.enabled = enabled;
        console.log('🖼️ 画布监控器:', enabled ? '✅ 启用' : '❌ 禁用');

        if (enabled) {
            // 检查控制器状态
            if (!this.controller) {
                console.error('❌ 画布监控器：控制器未设置');
                if (window.mattingDebugger) {
                    window.mattingDebugger.log('画布监控器：控制器未设置', 'error');
                }
                return;
            }

            this.startMonitoring();

            if (window.mattingDebugger) {
                window.mattingDebugger.log('画布监控器已启用', 'success');
            }
        } else {
            if (window.mattingDebugger) {
                window.mattingDebugger.log('画布监控器已禁用', 'info');
            }
        }
    }

    startMonitoring() {
        const monitor = () => {
            if (this.enabled && this.controller && this.controller.enabled) {
                this.processCanvasFrame();
            }
            requestAnimationFrame(monitor);
        };
        requestAnimationFrame(monitor);
    }

    async processCanvasFrame() {
        if (this.isProcessing) return;

        const now = performance.now();
        if (now - this.lastProcessTime < this.targetInterval) {
            return;
        }

        this.isProcessing = true;
        this.lastProcessTime = now;

        try {
            // 状态检查
            if (!this.enabled || !this.controller || !this.controller.enabled) {
                return;
            }

            // 查找画布元素
            const canvas = document.getElementById('canvas_video');
            if (!canvas) {
                console.warn('⚠️ 未找到canvas_video元素');
                if (window.mattingDebugger) {
                    window.mattingDebugger.log('未找到canvas_video元素', 'warn');
                }
                return;
            }

            const ctx = canvas.getContext('2d');
            if (!ctx) {
                console.warn('⚠️ 无法获取画布上下文');
                if (window.mattingDebugger) {
                    window.mattingDebugger.log('无法获取画布上下文', 'warn');
                }
                return;
            }

            const width = canvas.width;
            const height = canvas.height;

            if (width <= 0 || height <= 0) {
                console.warn('⚠️ 画布尺寸无效:', width, 'x', height);
                return;
            }

            // 获取图像数据
            const imageData = ctx.getImageData(0, 0, width, height);

            if (!imageData || !imageData.data || imageData.data.length === 0) {
                console.warn('⚠️ 图像数据无效');
                return;
            }

            // 应用抠图处理
            const processedData = await this.controller.process(imageData, width, height);

            // 绘制处理后的图像
            if (processedData && processedData.data) {
                ctx.putImageData(processedData, 0, 0);

                // 记录成功处理
                if (window.mattingDebugger && Math.random() < 0.01) { // 1%的概率记录，避免日志过多
                    window.mattingDebugger.log('画布抠图处理成功', 'success');
                }
            } else {
                console.warn('⚠️ 抠图处理返回无效数据');
                if (window.mattingDebugger) {
                    window.mattingDebugger.log('抠图处理返回无效数据', 'warn');
                }
            }

        } catch (error) {
            console.error('❌ 画布处理错误:', error);
            if (window.mattingDebugger) {
                window.mattingDebugger.log(`画布处理错误: ${error.message}`, 'error');
            }
        } finally {
            this.isProcessing = false;
        }
    }
}

// 创建简化监控器实例
let simpleCanvasMonitor = null;

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('开始初始化高级背景抠图集成...');

    // 创建简化监控器
    simpleCanvasMonitor = new SimpleCanvasMonitor();

    // 等待高级背景抠图控制器加载完成
    const initIntegration = () => {
        if (typeof advancedMattingController !== 'undefined' && advancedMattingController) {
            console.log('找到高级背景抠图控制器，开始集成...');

            // 设置控制器
            simpleCanvasMonitor.setController(advancedMattingController);

            // 尝试初始化完整集成
            try {
                advancedMattingIntegration = new AdvancedMattingIntegration();
                advancedMattingIntegration.initialize(advancedMattingController);
                console.log('完整集成初始化成功');
            } catch (error) {
                console.warn('完整集成初始化失败，使用简化模式:', error);
            }

        } else {
            setTimeout(initIntegration, 200);
        }
    };

    setTimeout(initIntegration, 1000); // 延迟初始化确保其他模块已加载
});

// 导出给其他模块使用
window.advancedMattingIntegration = advancedMattingIntegration;
window.simpleCanvasMonitor = simpleCanvasMonitor;
