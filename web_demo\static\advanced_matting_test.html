<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级背景抠图功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .feature-card {
            padding: 25px;
            border: 2px solid #4361ee;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(67, 97, 238, 0.3);
        }

        .feature-card h3 {
            margin-top: 0;
            color: #4361ee;
            border-bottom: 2px solid #4361ee;
            padding-bottom: 10px;
            font-size: 1.3em;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: '✨';
            position: absolute;
            left: 0;
            font-size: 16px;
        }

        .test-button {
            background: linear-gradient(135deg, #4361ee 0%, #3f37c9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
        }

        .test-button.secondary {
            background: linear-gradient(135deg, #4cc9f0 0%, #3ba3cc 100%);
        }

        .test-button.success {
            background: linear-gradient(135deg, #52b788 0%, #40916c 100%);
        }

        .demo-section {
            background: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .demo-canvas {
            width: 100%;
            max-width: 400px;
            height: 300px;
            border: 3px solid #4361ee;
            border-radius: 10px;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            margin: 15px auto;
            position: relative;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background-color: #52b788; }
        .status-warning { background-color: #f77f00; }
        .status-error { background-color: #d62828; }

        .performance-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4361ee;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4361ee;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .integration-info {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            border: 2px solid #2196f3;
        }

        .integration-info h3 {
            color: #1976d2;
            margin-top: 0;
        }

        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .test-container {
                padding: 20px;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎭 高级背景抠图功能测试</h1>

        <!-- 功能特性展示 -->
        <div class="feature-grid">
            <!-- 绿幕抠图算法 -->
            <div class="feature-card">
                <h3>🎨 高精度绿幕抠图</h3>
                <ul class="feature-list">
                    <li>基于HSV色彩空间的精确抠图</li>
                    <li>可调节色彩容差和饱和度阈值</li>
                    <li>绿色溢出抑制算法</li>
                    <li>实时参数调节</li>
                </ul>
                <button class="test-button" onclick="testGreenScreenAlgorithm()">测试绿幕算法</button>
            </div>

            <!-- 边缘处理 -->
            <div class="feature-card">
                <h3>✨ 高级边缘处理</h3>
                <ul class="feature-list">
                    <li>梯度羽化和距离变换</li>
                    <li>抗锯齿和亚像素平滑</li>
                    <li>双边滤波和形态学操作</li>
                    <li>Lanczos重采样</li>
                </ul>
                <button class="test-button" onclick="testEdgeProcessing()">测试边缘处理</button>
            </div>

            <!-- 透明背景输出 -->
            <div class="feature-card">
                <h3>🖼️ 透明背景输出</h3>
                <ul class="feature-list">
                    <li>完整保留前景细节</li>
                    <li>PNG格式透明通道支持</li>
                    <li>棋盘格透明度预览</li>
                    <li>一键导出功能</li>
                </ul>
                <button class="test-button secondary" onclick="testTransparentOutput()">测试透明输出</button>
            </div>

            <!-- 背景替换 -->
            <div class="feature-card">
                <h3>🌈 智能背景替换</h3>
                <ul class="feature-list">
                    <li>纯色、图片背景支持</li>
                    <li>自适应缩放和居中</li>
                    <li>图像效果调节</li>
                    <li>实时预览合成</li>
                </ul>
                <button class="test-button success" onclick="testBackgroundReplacement()">测试背景替换</button>
            </div>
        </div>

        <!-- 性能测试区域 -->
        <div class="demo-section">
            <h3>⚡ 性能测试</h3>
            <div class="demo-grid">
                <div>
                    <div class="demo-canvas" id="performanceDemo">
                        点击开始性能测试
                    </div>
                    <div style="text-align: center;">
                        <button class="test-button" onclick="runPerformanceTest()">开始性能测试</button>
                        <button class="test-button secondary" onclick="runStressTest()">压力测试</button>
                    </div>
                </div>

                <div class="performance-stats">
                    <h4>实时性能统计</h4>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="currentFps">--</div>
                            <div class="stat-label">处理帧率 (FPS)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="avgProcessTime">--</div>
                            <div class="stat-label">平均处理时间 (ms)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="memoryUsage">--</div>
                            <div class="stat-label">内存使用 (MB)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="qualityLevel">中等</div>
                            <div class="stat-label">当前质量等级</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 集成测试 -->
        <div class="integration-info">
            <h3>🔗 数字人系统集成</h3>
            <p>高级背景抠图功能已完全集成到数字人直播系统中，提供以下增强功能：</p>

            <div class="demo-grid">
                <div>
                    <h4>集成特性：</h4>
                    <ul class="feature-list">
                        <li>无缝集成现有视频处理流程</li>
                        <li>智能性能优化和自适应降级</li>
                        <li>实时错误处理和恢复机制</li>
                        <li>向后兼容传统抠图功能</li>
                    </ul>
                </div>

                <div>
                    <h4>使用方法：</h4>
                    <div class="code-snippet">
// 在数字人系统中启用高级抠图
advancedMattingController.enabled = true;

// 设置绿幕参数
advancedMattingController.greenScreen.setParameters({
    colorTolerance: 20,
    saturationMin: 0.3,
    valueMin: 0.2
});

// 选择背景类型
advancedMattingController.setBackground('transparent');
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="test-button" onclick="openMainDemo()">打开数字人演示</button>
                <button class="test-button secondary" onclick="testIntegration()">测试集成功能</button>
            </div>
        </div>

        <!-- 兼容性信息 -->
        <div class="demo-section">
            <h3>🔧 浏览器兼容性</h3>
            <div id="compatibilityResults" class="demo-grid">
                <!-- 兼容性结果将在这里显示 -->
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="test-button" onclick="checkCompatibility()">检查兼容性</button>
            </div>
        </div>
    </div>

    <!-- 引入高级背景抠图模块 -->
    <script src="js/advanced_green_screen.js"></script>
    <script src="js/transparent_background.js"></script>
    <script src="js/edge_processing.js"></script>
    <script src="js/background_replacement.js"></script>
    <script src="js/advanced_matting_controller.js"></script>

    <script>
        // 测试用的全局变量
        let testController = null;
        let performanceTestRunning = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('高级背景抠图测试页面加载完成');

            // 初始化测试控制器
            if (typeof AdvancedMattingController !== 'undefined') {
                testController = new AdvancedMattingController();
                console.log('测试控制器初始化完成');
            }

            // 自动检查兼容性
            setTimeout(checkCompatibility, 1000);
        });

        // 测试绿幕算法
        function testGreenScreenAlgorithm() {
            console.log('测试绿幕抠图算法...');

            if (!testController) {
                alert('测试控制器未初始化');
                return;
            }

            // 创建测试图像
            const canvas = document.createElement('canvas');
            canvas.width = 640;
            canvas.height = 480;
            const ctx = canvas.getContext('2d');

            // 绘制绿色背景和前景对象
            ctx.fillStyle = '#00FF00';
            ctx.fillRect(0, 0, 640, 480);

            ctx.fillStyle = '#FF0000';
            ctx.fillRect(200, 150, 240, 180);

            const imageData = ctx.getImageData(0, 0, 640, 480);

            // 测试绿幕算法
            const startTime = performance.now();
            const result = testController.greenScreen.process(imageData, 640, 480);
            const endTime = performance.now();

            alert(`绿幕算法测试完成！\n处理时间: ${(endTime - startTime).toFixed(2)}ms\n检测到前景像素: ${result.maskData.filter(v => v > 0.5).length}`);
        }

        // 测试边缘处理
        function testEdgeProcessing() {
            console.log('测试边缘处理算法...');

            // 创建测试遮罩
            const width = 100, height = 100;
            const maskData = new Float32Array(width * height);

            // 创建一个圆形遮罩
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = 30;

            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                    maskData[y * width + x] = distance < radius ? 1 : 0;
                }
            }

            // 测试边缘处理
            if (testController && testController.edgeProcessor) {
                const startTime = performance.now();
                const processedMask = testController.edgeProcessor.processEdges(maskData, width, height);
                const endTime = performance.now();

                alert(`边缘处理测试完成！\n处理时间: ${(endTime - startTime).toFixed(2)}ms\n平滑度提升: ${((processedMask.reduce((a, b) => a + b, 0) / processedMask.length) * 100).toFixed(1)}%`);
            } else {
                alert('边缘处理器未初始化');
            }
        }

        // 测试透明输出
        function testTransparentOutput() {
            console.log('测试透明背景输出...');

            if (testController && testController.transparentBg) {
                // 创建测试图像和遮罩
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');

                // 绘制测试图像
                const gradient = ctx.createLinearGradient(0, 0, 200, 200);
                gradient.addColorStop(0, '#FF0000');
                gradient.addColorStop(1, '#0000FF');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 200, 200);

                const imageData = ctx.getImageData(0, 0, 200, 200);

                // 创建圆形遮罩
                const maskData = new Float32Array(200 * 200);
                const centerX = 100, centerY = 100, radius = 80;

                for (let y = 0; y < 200; y++) {
                    for (let x = 0; x < 200; x++) {
                        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                        maskData[y * 200 + x] = Math.max(0, Math.min(1, (radius - distance) / 10));
                    }
                }

                // 处理透明背景
                const result = testController.transparentBg.processTransparentBackground(imageData, maskData, 200, 200);

                // 导出PNG
                const filename = `transparent_test_${Date.now()}.png`;
                testController.transparentBg.downloadImage(filename);

                alert(`透明背景测试完成！\n已导出文件: ${filename}`);
            } else {
                alert('透明背景处理器未初始化');
            }
        }

        // 测试背景替换
        function testBackgroundReplacement() {
            console.log('测试背景替换...');

            if (testController && testController.bgReplacement) {
                // 设置彩色背景
                testController.bgReplacement.setBackground('color', '#FF6B6B');

                // 创建测试数据
                const canvas = document.createElement('canvas');
                canvas.width = 300;
                canvas.height = 200;
                const ctx = canvas.getContext('2d');

                // 绘制前景
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(50, 50, 200, 100);

                const imageData = ctx.getImageData(0, 0, 300, 200);

                // 创建遮罩
                const maskData = new Float32Array(300 * 200);
                for (let i = 0; i < maskData.length; i++) {
                    const x = i % 300;
                    const y = Math.floor(i / 300);
                    maskData[i] = (x >= 50 && x < 250 && y >= 50 && y < 150) ? 1 : 0;
                }

                // 合成背景
                const result = testController.bgReplacement.composite(imageData, maskData, 300, 200);

                alert('背景替换测试完成！\n已成功合成彩色背景');
            } else {
                alert('背景替换器未初始化');
            }
        }

        // 运行性能测试
        function runPerformanceTest() {
            if (performanceTestRunning) return;

            performanceTestRunning = true;
            const demo = document.getElementById('performanceDemo');
            demo.textContent = '性能测试进行中...';

            const testFrames = 100;
            let processedFrames = 0;
            const processingTimes = [];

            const testFrame = () => {
                const startTime = performance.now();

                // 模拟图像处理
                if (testController) {
                    const canvas = document.createElement('canvas');
                    canvas.width = 640;
                    canvas.height = 480;
                    const ctx = canvas.getContext('2d');

                    // 生成随机图像
                    const imageData = ctx.createImageData(640, 480);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        imageData.data[i] = Math.random() * 255;     // R
                        imageData.data[i + 1] = Math.random() * 255; // G
                        imageData.data[i + 2] = Math.random() * 255; // B
                        imageData.data[i + 3] = 255;                 // A
                    }

                    // 处理图像
                    testController.process(imageData, 640, 480);
                }

                const endTime = performance.now();
                processingTimes.push(endTime - startTime);
                processedFrames++;

                // 更新进度
                demo.textContent = `测试进度: ${processedFrames}/${testFrames}`;

                if (processedFrames < testFrames) {
                    requestAnimationFrame(testFrame);
                } else {
                    // 测试完成
                    const avgTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
                    const fps = Math.round(1000 / avgTime);

                    demo.textContent = `测试完成: ${fps} FPS`;

                    // 更新统计显示
                    document.getElementById('currentFps').textContent = fps;
                    document.getElementById('avgProcessTime').textContent = avgTime.toFixed(2);

                    performanceTestRunning = false;
                }
            };

            requestAnimationFrame(testFrame);
        }

        // 压力测试
        function runStressTest() {
            alert('压力测试将连续运行高强度处理，可能影响系统性能。\n建议在测试环境中运行。');
            // 这里可以实现更复杂的压力测试
        }

        // 检查兼容性
        function checkCompatibility() {
            const tests = {
                'Canvas 2D': () => {
                    try {
                        const canvas = document.createElement('canvas');
                        return !!(canvas.getContext && canvas.getContext('2d'));
                    } catch (e) {
                        return false;
                    }
                },
                'WebGL': () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        return !!gl;
                    } catch (e) {
                        return false;
                    }
                },
                'File API': () => {
                    return !!(window.File && window.FileReader && window.FileList && window.Blob);
                },
                'Web Workers': () => {
                    return typeof Worker !== 'undefined';
                },
                'ImageData': () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        return !!(ctx && ctx.createImageData);
                    } catch (e) {
                        return false;
                    }
                },
                'Float32Array': () => {
                    return typeof Float32Array !== 'undefined';
                }
            };

            const resultsContainer = document.getElementById('compatibilityResults');
            resultsContainer.innerHTML = '';

            Object.entries(tests).forEach(([name, test]) => {
                const result = test();
                const card = document.createElement('div');
                card.className = 'feature-card';
                card.innerHTML = `
                    <h4><span class="status-indicator ${result ? 'status-success' : 'status-error'}"></span>${name}</h4>
                    <p>${result ? '✅ 支持' : '❌ 不支持'}</p>
                `;
                resultsContainer.appendChild(card);
            });
        }

        // 打开主演示
        function openMainDemo() {
            window.open('MiniLive_RealTime.html', '_blank');
        }

        // 测试集成功能
        function testIntegration() {
            alert('集成测试说明：\n\n1. 打开数字人演示页面\n2. 在控制面板中找到"高级背景抠图"区域\n3. 启用抠图功能\n4. 调节各项参数\n5. 测试不同背景类型\n6. 观察性能监控数据');
        }

        // 定期更新内存使用情况
        setInterval(() => {
            if (performance.memory) {
                const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                const memoryElement = document.getElementById('memoryUsage');
                if (memoryElement) {
                    memoryElement.textContent = memoryMB;
                }
            }
        }, 2000);
    </script>
</body>
</html>