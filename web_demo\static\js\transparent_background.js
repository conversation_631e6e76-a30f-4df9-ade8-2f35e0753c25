/**
 * 透明背景输出模块
 * 处理抠图后的透明背景输出和PNG格式导出
 */

class TransparentBackground {
    constructor() {
        this.outputCanvas = document.createElement('canvas');
        this.outputCtx = this.outputCanvas.getContext('2d');
        this.previewCanvas = document.createElement('canvas');
        this.previewCtx = this.previewCanvas.getContext('2d');
        
        // 输出设置
        this.outputFormat = 'png';  // png, webp
        this.quality = 0.9;         // 输出质量 (0-1)
        this.enablePreview = true;  // 启用实时预览
        
        // 透明度处理设置
        this.alphaThreshold = 0.01; // Alpha阈值，低于此值设为完全透明
        this.premultiplyAlpha = false; // 是否预乘Alpha
        
        // 背景替换设置
        this.backgroundType = 'transparent'; // transparent, color, image
        this.backgroundColor = '#00FF00';    // 替换背景颜色
        this.backgroundImage = null;         // 替换背景图片
        
        // 性能监控
        this.processingTimes = [];
    }
    
    /**
     * 设置输出画布尺寸
     */
    setCanvasSize(width, height) {
        this.outputCanvas.width = width;
        this.outputCanvas.height = height;
        this.previewCanvas.width = width;
        this.previewCanvas.height = height;
    }
    
    /**
     * 处理透明背景
     * @param {ImageData} imageData - 输入图像数据
     * @param {Float32Array} maskData - 透明度遮罩
     * @param {number} width - 图像宽度
     * @param {number} height - 图像高度
     * @returns {ImageData} 处理后的图像数据
     */
    processTransparentBackground(imageData, maskData, width, height) {
        const startTime = performance.now();
        
        this.setCanvasSize(width, height);
        
        const data = imageData.data;
        const outputImageData = this.outputCtx.createImageData(width, height);
        const outputData = outputImageData.data;
        
        // 处理每个像素
        for (let i = 0; i < data.length; i += 4) {
            const pixelIndex = Math.floor(i / 4);
            const alpha = maskData[pixelIndex];
            
            // 应用Alpha阈值
            const finalAlpha = alpha < this.alphaThreshold ? 0 : alpha;
            
            if (this.premultiplyAlpha && finalAlpha > 0) {
                // 预乘Alpha
                outputData[i] = Math.round(data[i] * finalAlpha);     // R
                outputData[i + 1] = Math.round(data[i + 1] * finalAlpha); // G
                outputData[i + 2] = Math.round(data[i + 2] * finalAlpha); // B
                outputData[i + 3] = Math.round(finalAlpha * 255);     // A
            } else {
                // 直接Alpha
                outputData[i] = data[i];                              // R
                outputData[i + 1] = data[i + 1];                      // G
                outputData[i + 2] = data[i + 2];                      // B
                outputData[i + 3] = Math.round(finalAlpha * 255);     // A
            }
        }
        
        // 更新输出画布
        this.outputCtx.putImageData(outputImageData, 0, 0);
        
        // 生成预览（如果启用）
        if (this.enablePreview) {
            this.generatePreview(outputImageData, width, height);
        }
        
        // 性能统计
        const processingTime = performance.now() - startTime;
        this.processingTimes.push(processingTime);
        if (this.processingTimes.length > 30) {
            this.processingTimes.shift();
        }
        
        return outputImageData;
    }
    
    /**
     * 生成带背景的预览
     */
    generatePreview(imageData, width, height) {
        // 清空预览画布
        this.previewCtx.clearRect(0, 0, width, height);
        
        // 绘制背景
        if (this.backgroundType === 'color') {
            this.previewCtx.fillStyle = this.backgroundColor;
            this.previewCtx.fillRect(0, 0, width, height);
        } else if (this.backgroundType === 'image' && this.backgroundImage) {
            this.drawBackgroundImage(width, height);
        } else {
            // 透明背景 - 绘制棋盘格图案
            this.drawTransparencyPattern(width, height);
        }
        
        // 绘制前景
        this.previewCtx.putImageData(imageData, 0, 0);
    }
    
    /**
     * 绘制透明度棋盘格图案
     */
    drawTransparencyPattern(width, height) {
        const tileSize = 20;
        const color1 = '#FFFFFF';
        const color2 = '#E0E0E0';
        
        for (let y = 0; y < height; y += tileSize) {
            for (let x = 0; x < width; x += tileSize) {
                const isEven = (Math.floor(x / tileSize) + Math.floor(y / tileSize)) % 2 === 0;
                this.previewCtx.fillStyle = isEven ? color1 : color2;
                this.previewCtx.fillRect(x, y, 
                    Math.min(tileSize, width - x), 
                    Math.min(tileSize, height - y));
            }
        }
    }
    
    /**
     * 绘制背景图片（自适应缩放和居中）
     */
    drawBackgroundImage(width, height) {
        if (!this.backgroundImage) return;
        
        const imgWidth = this.backgroundImage.width;
        const imgHeight = this.backgroundImage.height;
        
        // 计算缩放比例以填充整个画布
        const scaleX = width / imgWidth;
        const scaleY = height / imgHeight;
        const scale = Math.max(scaleX, scaleY);
        
        // 计算居中位置
        const scaledWidth = imgWidth * scale;
        const scaledHeight = imgHeight * scale;
        const offsetX = (width - scaledWidth) / 2;
        const offsetY = (height - scaledHeight) / 2;
        
        // 绘制背景图片
        this.previewCtx.drawImage(
            this.backgroundImage,
            offsetX, offsetY,
            scaledWidth, scaledHeight
        );
    }
    
    /**
     * 导出为PNG格式
     * @param {string} filename - 文件名
     * @returns {string} Data URL
     */
    exportToPNG(filename = 'transparent_output.png') {
        const dataURL = this.outputCanvas.toDataURL('image/png', this.quality);
        
        // 创建下载链接
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        
        return dataURL;
    }
    
    /**
     * 导出为WebP格式（如果浏览器支持）
     */
    exportToWebP(filename = 'transparent_output.webp') {
        if (!this.outputCanvas.toDataURL('image/webp').startsWith('data:image/webp')) {
            console.warn('浏览器不支持WebP格式导出');
            return this.exportToPNG(filename.replace('.webp', '.png'));
        }
        
        const dataURL = this.outputCanvas.toDataURL('image/webp', this.quality);
        
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        
        return dataURL;
    }
    
    /**
     * 自动下载导出的图片
     */
    downloadImage(filename) {
        let dataURL;
        
        if (this.outputFormat === 'webp') {
            dataURL = this.exportToWebP(filename);
        } else {
            dataURL = this.exportToPNG(filename);
        }
        
        // 触发下载
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataURL;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        return dataURL;
    }
    
    /**
     * 设置背景替换
     */
    setBackground(type, value) {
        this.backgroundType = type;
        
        switch (type) {
            case 'color':
                this.backgroundColor = value;
                break;
            case 'image':
                if (value instanceof Image) {
                    this.backgroundImage = value;
                } else if (typeof value === 'string') {
                    // 从URL加载图片
                    const img = new Image();
                    img.onload = () => {
                        this.backgroundImage = img;
                    };
                    img.src = value;
                }
                break;
            case 'transparent':
                // 无需额外设置
                break;
        }
    }
    
    /**
     * 获取预览画布
     */
    getPreviewCanvas() {
        return this.previewCanvas;
    }
    
    /**
     * 获取输出画布
     */
    getOutputCanvas() {
        return this.outputCanvas;
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (this.processingTimes.length === 0) {
            return { avgTime: 0, fps: 0 };
        }
        
        const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
        const fps = Math.round(1000 / avgTime);
        
        return {
            avgTime: avgTime.toFixed(2),
            fps: fps
        };
    }
    
    /**
     * 设置输出参数
     */
    setOutputSettings(settings) {
        if (settings.format) this.outputFormat = settings.format;
        if (settings.quality !== undefined) this.quality = settings.quality;
        if (settings.alphaThreshold !== undefined) this.alphaThreshold = settings.alphaThreshold;
        if (settings.premultiplyAlpha !== undefined) this.premultiplyAlpha = settings.premultiplyAlpha;
        if (settings.enablePreview !== undefined) this.enablePreview = settings.enablePreview;
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return {
            outputFormat: this.outputFormat,
            quality: this.quality,
            alphaThreshold: this.alphaThreshold,
            premultiplyAlpha: this.premultiplyAlpha,
            enablePreview: this.enablePreview,
            backgroundType: this.backgroundType,
            backgroundColor: this.backgroundColor
        };
    }
}

// 导出模块
window.TransparentBackground = TransparentBackground;
