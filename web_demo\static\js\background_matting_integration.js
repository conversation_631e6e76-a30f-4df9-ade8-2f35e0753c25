// 背景抠图集成模块
// 用于将背景抠图功能集成到现有的数字人视频处理流程中

class MattingIntegration {
    constructor() {
        this.originalProcessVideoFrames = null;
        this.originalProcessDataSet = null;
        this.isIntegrated = false;
        this.mattingProcessor = null;
    }

    // 初始化集成
    async initialize(mattingProcessor) {
        this.mattingProcessor = mattingProcessor;
        
        // 等待原始函数加载
        await this.waitForOriginalFunctions();
        
        // 备份原始函数
        this.backupOriginalFunctions();
        
        // 替换为增强版本
        this.replaceWithEnhancedFunctions();
        
        this.isIntegrated = true;
        console.log('背景抠图集成完成');
    }

    // 等待原始函数加载
    async waitForOriginalFunctions() {
        return new Promise((resolve) => {
            const checkFunctions = () => {
                if (typeof processVideoFrames !== 'undefined' && 
                    typeof processDataSet !== 'undefined') {
                    resolve();
                } else {
                    setTimeout(checkFunctions, 100);
                }
            };
            checkFunctions();
        });
    }

    // 备份原始函数
    backupOriginalFunctions() {
        if (typeof processVideoFrames !== 'undefined') {
            this.originalProcessVideoFrames = processVideoFrames;
        }
        if (typeof processDataSet !== 'undefined') {
            this.originalProcessDataSet = processDataSet;
        }
    }

    // 替换为增强版本
    replaceWithEnhancedFunctions() {
        const self = this;

        // 增强的视频帧处理函数
        window.processVideoFrames = async function() {
            if (isPaused) {
                return;
            }

            // 检查视频帧是否已经解码完成
            if (videoProcessor.videoFrames.length === 0 || 
                videoProcessor.videoFrames.length < videoProcessor.nbSampleTotal) {
                console.log('Waiting for video frames to load...', 
                           videoProcessor.videoFrames.length, videoProcessor.nbSampleTotal);
                setTimeout(processVideoFrames, 100);
                return;
            }

            if (frameIndex >= videoProcessor.videoFrames.length) {
                frameIndex = 0;
            }

            // 绘制视频帧
            if (tag_ios17) {
                const { blob, duration, timestamp } = videoProcessor.videoFrames[frameIndex];
                const img = await createImageBitmap(blob);
                ctx_video.drawImage(img, 0, 0, canvas_video.width, canvas_video.height);
                img.close();
            } else {
                const { img, duration, timestamp } = videoProcessor.videoFrames[frameIndex];
                ctx_video.drawImage(img, 0, 0, canvas_video.width, canvas_video.height);
            }

            // 应用背景抠图处理
            if (self.mattingProcessor && self.mattingProcessor.enabled) {
                await self.mattingProcessor.processFrame(canvas_video, ctx_video);
            }

            // 计算并显示FPS
            if (fps_enabled) {
                const currentTime = performance.now();
                const deltaTime = currentTime - lastFrameTime;
                frameTimes.push(currentTime);

                while (frameTimes.length > 0 && currentTime - frameTimes[0] > 1000) {
                    frameTimes.shift();
                }

                const fps = frameTimes.length;
                ctx_video.fillStyle = 'white';
                ctx_video.font = '16px Arial';
                ctx_video.textAlign = 'right';
                ctx_video.fillText(`FPS: ${fps}`, canvas_video.width - 10, 20);
            }

            processDataSet(frameIndex);
            frameIndex++;

            const currentTime = performance.now();
            const deltaTime = currentTime - lastFrameTime;
            const delay = Math.max(0, frameInterval - deltaTime);
            lastFrameTime = currentTime + delay;
            setTimeout(processVideoFrames, delay);
        };

        // 增强的数据集处理函数
        window.processDataSet = async function(currentDataSetIndex) {
            if (isPaused) {
                return;
            }

            const dataSet = dataSets[currentDataSetIndex];
            const rect = dataSet.rect;
            const currentpoints = dataSets[currentDataSetIndex].points;

            const matrix = new Float32Array(16);
            matrix.set(currentpoints.slice(0, 16));

            const subPoints = currentpoints.slice(16);
            Module._updateBlendShape(bsPtr, 12 * 4);
            const bsArray = new Float32Array(Module.HEAPU8.buffer, bsPtr, 12);

            render(matrix, subPoints, bsArray);
            resizedCtx.drawImage(canvas_video, rect[0], rect[1], 
                               rect[2] - rect[0], rect[3] - rect[1], 0, 0, 128, 128);

            const imageData = resizedCtx.getImageData(0, 0, 128, 128);
            Module.HEAPU8.set(imageData.data, imageDataPtr);
            Module.HEAPU8.set(pixels_fbo, imageDataGlPtr);
            Module._processImage(imageDataPtr, 128, 128, imageDataGlPtr, 128, 128);
            
            const result = Module.HEAPU8.subarray(imageDataPtr, imageDataPtr + imageData.data.length);
            imageData.data.set(result);

            resizedCtx.putImageData(imageData, 0, 0);
            
            // 在最终绘制前再次应用抠图处理（如果需要）
            ctx_video.drawImage(resizedCanvas, 0, 0, 128, 128, 
                              rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1]);
        };
    }

    // 恢复原始函数
    restore() {
        if (this.originalProcessVideoFrames) {
            window.processVideoFrames = this.originalProcessVideoFrames;
        }
        if (this.originalProcessDataSet) {
            window.processDataSet = this.originalProcessDataSet;
        }
        this.isIntegrated = false;
        console.log('背景抠图集成已恢复');
    }

    // 检查集成状态
    isReady() {
        return this.isIntegrated && this.mattingProcessor;
    }

    // 获取性能统计
    getPerformanceStats() {
        if (this.mattingProcessor) {
            return {
                enabled: this.mattingProcessor.enabled,
                mode: this.mattingProcessor.mode,
                avgProcessingTime: this.mattingProcessor.processingTimes.length > 0 ?
                    this.mattingProcessor.processingTimes.reduce((a, b) => a + b, 0) / 
                    this.mattingProcessor.processingTimes.length : 0,
                frameCount: this.mattingProcessor.processingTimes.length
            };
        }
        return null;
    }
}

// 全局集成实例
let mattingIntegration = null;

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 等待背景抠图模块加载完成
    const initIntegration = () => {
        if (typeof backgroundMatting !== 'undefined' && backgroundMatting) {
            mattingIntegration = new MattingIntegration();
            mattingIntegration.initialize(backgroundMatting);
        } else {
            setTimeout(initIntegration, 100);
        }
    };
    
    setTimeout(initIntegration, 500); // 延迟初始化确保其他模块已加载
});

// 导出给其他模块使用
window.mattingIntegration = mattingIntegration;
