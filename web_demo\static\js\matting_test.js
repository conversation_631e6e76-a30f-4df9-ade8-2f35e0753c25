/**
 * 高级背景抠图功能测试脚本
 * 用于验证修复后的抠图功能是否正常工作
 */

class MattingTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
        
        console.log('抠图功能测试器初始化');
    }
    
    /**
     * 运行所有测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.log('测试正在进行中...');
            return;
        }
        
        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 开始运行抠图功能测试...');
        
        try {
            // 基础功能测试
            await this.testBasicFunctionality();
            
            // 事件绑定测试
            await this.testEventBinding();
            
            // 参数调节测试
            await this.testParameterAdjustment();
            
            // 背景替换测试
            await this.testBackgroundReplacement();
            
            // 性能测试
            await this.testPerformance();

            // 透明背景专项测试
            await this.testTransparentBackground();

            // 显示测试结果
            this.showTestResults();
            
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        } finally {
            this.isRunning = false;
        }
    }
    
    /**
     * 基础功能测试
     */
    async testBasicFunctionality() {
        console.log('🔍 测试基础功能...');
        
        const tests = [
            {
                name: '控制器实例存在',
                test: () => typeof window.advancedMattingController !== 'undefined' && window.advancedMattingController !== null
            },
            {
                name: '简化监控器存在',
                test: () => typeof window.simpleCanvasMonitor !== 'undefined' && window.simpleCanvasMonitor !== null
            },
            {
                name: '抠图开关元素存在',
                test: () => document.getElementById('advancedMattingToggle') !== null
            },
            {
                name: '画布元素存在',
                test: () => document.getElementById('canvas_video') !== null
            },
            {
                name: '所有必要模块已加载',
                test: () => {
                    const modules = ['AdvancedGreenScreen', 'TransparentBackground', 'EdgeProcessing', 'BackgroundReplacement', 'AdvancedMattingController'];
                    return modules.every(module => typeof window[module] !== 'undefined');
                }
            }
        ];
        
        for (const test of tests) {
            const result = test.test();
            this.testResults.push({
                category: '基础功能',
                name: test.name,
                passed: result,
                details: result ? '通过' : '失败'
            });
            console.log(`  ${result ? '✅' : '❌'} ${test.name}`);
        }
    }
    
    /**
     * 事件绑定测试
     */
    async testEventBinding() {
        console.log('🔍 测试事件绑定...');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (!toggle) {
            this.testResults.push({
                category: '事件绑定',
                name: '抠图开关事件测试',
                passed: false,
                details: '抠图开关元素不存在'
            });
            return;
        }
        
        // 测试点击事件
        const initialState = window.advancedMattingController ? window.advancedMattingController.enabled : false;
        
        // 模拟点击
        toggle.click();
        
        // 等待状态更新
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const newState = window.advancedMattingController ? window.advancedMattingController.enabled : false;
        const stateChanged = initialState !== newState;
        
        this.testResults.push({
            category: '事件绑定',
            name: '抠图开关点击响应',
            passed: stateChanged,
            details: stateChanged ? `状态从 ${initialState} 变为 ${newState}` : '状态未改变'
        });
        
        console.log(`  ${stateChanged ? '✅' : '❌'} 抠图开关点击响应`);
        
        // 恢复初始状态
        if (stateChanged) {
            toggle.click();
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    /**
     * 参数调节测试
     */
    async testParameterAdjustment() {
        console.log('🔍 测试参数调节...');
        
        const sliders = [
            'colorToleranceSlider',
            'saturationSlider',
            'brightnessSlider',
            'featherRadiusSlider',
            'antiAliasSlider',
            'smoothingSlider'
        ];
        
        for (const sliderId of sliders) {
            const slider = document.getElementById(sliderId);
            const exists = slider !== null;
            
            let canAdjust = false;
            if (exists) {
                const originalValue = slider.value;
                slider.value = parseFloat(originalValue) + 1;
                
                // 触发input事件
                const event = new Event('input', { bubbles: true });
                slider.dispatchEvent(event);
                
                // 检查值是否改变
                canAdjust = slider.value !== originalValue;
                
                // 恢复原值
                slider.value = originalValue;
                slider.dispatchEvent(event);
            }
            
            this.testResults.push({
                category: '参数调节',
                name: `${sliderId} 调节`,
                passed: exists && canAdjust,
                details: exists ? (canAdjust ? '可调节' : '无法调节') : '元素不存在'
            });
            
            console.log(`  ${exists && canAdjust ? '✅' : '❌'} ${sliderId}`);
        }
    }
    
    /**
     * 背景替换测试
     */
    async testBackgroundReplacement() {
        console.log('🔍 测试背景替换...');

        const bgButtons = [
            { id: 'transparentBgBtn', type: 'transparent', name: '透明背景' },
            { id: 'colorBgBtn', type: 'color', name: '纯色背景' },
            { id: 'imageBgBtn', type: 'image', name: '图片背景' }
        ];

        for (const buttonInfo of bgButtons) {
            const button = document.getElementById(buttonInfo.id);
            const exists = button !== null;

            let canClick = false;
            let isDefaultActive = false;

            if (exists) {
                // 检查透明背景是否默认激活
                if (buttonInfo.type === 'transparent') {
                    isDefaultActive = button.classList.contains('active');
                }

                try {
                    button.click();
                    canClick = true;

                    // 等待状态更新
                    await new Promise(resolve => setTimeout(resolve, 100));

                    // 验证控制器状态
                    if (window.advancedMattingController) {
                        const currentType = window.advancedMattingController.currentBackgroundType;
                        const typeMatches = currentType === buttonInfo.type;

                        this.testResults.push({
                            category: '背景替换',
                            name: `${buttonInfo.name} 状态同步`,
                            passed: typeMatches,
                            details: typeMatches ? '状态同步正确' : `期望${buttonInfo.type}，实际${currentType}`
                        });
                    }

                } catch (error) {
                    console.warn(`点击 ${buttonInfo.id} 时出错:`, error);
                }
            }

            this.testResults.push({
                category: '背景替换',
                name: `${buttonInfo.name} 按钮`,
                passed: exists && canClick,
                details: exists ? (canClick ? '可点击' : '点击失败') : '元素不存在'
            });

            // 特别测试透明背景默认状态
            if (buttonInfo.type === 'transparent') {
                this.testResults.push({
                    category: '背景替换',
                    name: '透明背景默认激活',
                    passed: isDefaultActive,
                    details: isDefaultActive ? '默认激活正确' : '默认未激活'
                });
            }

            console.log(`  ${exists && canClick ? '✅' : '❌'} ${buttonInfo.name}`);
        }

        // 测试透明背景信息显示
        const transparentInfo = document.getElementById('transparentBgInfo');
        const infoExists = transparentInfo !== null;
        const infoVisible = infoExists && transparentInfo.style.display !== 'none';

        this.testResults.push({
            category: '背景替换',
            name: '透明背景信息显示',
            passed: infoExists && infoVisible,
            details: infoExists ? (infoVisible ? '信息正确显示' : '信息未显示') : '信息元素不存在'
        });

        console.log(`  ${infoExists && infoVisible ? '✅' : '❌'} 透明背景信息显示`);
    }
    
    /**
     * 性能测试
     */
    async testPerformance() {
        console.log('🔍 测试性能...');
        
        const controller = window.advancedMattingController;
        if (!controller) {
            this.testResults.push({
                category: '性能',
                name: '性能测试',
                passed: false,
                details: '控制器不存在'
            });
            return;
        }
        
        // 启用抠图功能
        const wasEnabled = controller.enabled;
        if (!wasEnabled) {
            controller.enabled = true;
        }
        
        // 等待几秒钟收集性能数据
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查FPS显示
        const fpsElement = document.getElementById('advancedProcessingFps');
        const hasFpsDisplay = fpsElement && fpsElement.textContent !== '--';
        
        this.testResults.push({
            category: '性能',
            name: 'FPS显示',
            passed: hasFpsDisplay,
            details: hasFpsDisplay ? `FPS: ${fpsElement.textContent}` : '无FPS显示'
        });
        
        console.log(`  ${hasFpsDisplay ? '✅' : '❌'} FPS显示`);
        
        // 恢复原始状态
        if (!wasEnabled) {
            controller.enabled = false;
        }
    }

    /**
     * 透明背景专项测试
     */
    async testTransparentBackground() {
        console.log('🔍 测试透明背景功能...');

        const controller = window.advancedMattingController;
        if (!controller) {
            this.testResults.push({
                category: '透明背景',
                name: '控制器可用性',
                passed: false,
                details: '控制器不存在'
            });
            return;
        }

        // 测试默认背景类型
        const defaultIsTransparent = controller.currentBackgroundType === 'transparent';
        this.testResults.push({
            category: '透明背景',
            name: '默认背景类型',
            passed: defaultIsTransparent,
            details: defaultIsTransparent ? '默认为透明背景' : `默认为${controller.currentBackgroundType}`
        });

        // 测试透明背景模块
        const transparentBgModule = controller.transparentBg;
        const moduleExists = transparentBgModule !== null && transparentBgModule !== undefined;
        this.testResults.push({
            category: '透明背景',
            name: '透明背景模块',
            passed: moduleExists,
            details: moduleExists ? '模块存在' : '模块不存在'
        });

        if (moduleExists) {
            // 测试模块设置
            const settings = transparentBgModule.getSettings();
            const hasCorrectSettings = settings && settings.backgroundType === 'transparent';
            this.testResults.push({
                category: '透明背景',
                name: '模块设置',
                passed: hasCorrectSettings,
                details: hasCorrectSettings ? '设置正确' : '设置错误'
            });

            // 测试Alpha阈值设置
            const alphaThreshold = transparentBgModule.alphaThreshold;
            const thresholdOk = alphaThreshold >= 0 && alphaThreshold <= 1;
            this.testResults.push({
                category: '透明背景',
                name: 'Alpha阈值',
                passed: thresholdOk,
                details: thresholdOk ? `阈值: ${alphaThreshold}` : '阈值设置错误'
            });

            // 测试抗锯齿功能
            const antiAliasing = transparentBgModule.enableAntiAliasing;
            this.testResults.push({
                category: '透明背景',
                name: '抗锯齿功能',
                passed: antiAliasing === true,
                details: antiAliasing ? '已启用' : '未启用'
            });
        }

        // 测试背景替换模块的透明支持
        const bgReplacementModule = controller.bgReplacement;
        if (bgReplacementModule) {
            const bgType = bgReplacementModule.backgroundType;
            const supportsTransparent = bgType === 'transparent';
            this.testResults.push({
                category: '透明背景',
                name: '背景替换模块透明支持',
                passed: supportsTransparent,
                details: supportsTransparent ? '支持透明背景' : `当前类型: ${bgType}`
            });
        }

        // 测试UI元素
        const transparentBtn = document.getElementById('transparentBgBtn');
        const btnActive = transparentBtn && transparentBtn.classList.contains('active');
        this.testResults.push({
            category: '透明背景',
            name: '透明背景按钮状态',
            passed: btnActive,
            details: btnActive ? '按钮已激活' : '按钮未激活'
        });

        console.log('透明背景专项测试完成');
    }

    /**
     * 显示测试结果
     */
    showTestResults() {
        console.log('\n🧪 测试结果总结:');
        console.log('==================');
        
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        let totalTests = 0;
        let passedTests = 0;
        
        categories.forEach(category => {
            const categoryResults = this.testResults.filter(r => r.category === category);
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            
            console.log(`\n${category}:`);
            categoryResults.forEach(result => {
                console.log(`  ${result.passed ? '✅' : '❌'} ${result.name}: ${result.details}`);
            });
            
            console.log(`  小计: ${categoryPassed}/${categoryResults.length} 通过`);
            
            totalTests += categoryResults.length;
            passedTests += categoryPassed;
        });
        
        console.log('\n==================');
        console.log(`总计: ${passedTests}/${totalTests} 测试通过 (${Math.round(passedTests/totalTests*100)}%)`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！抠图功能工作正常。');
        } else {
            console.log('⚠️ 部分测试失败，请检查相关功能。');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            percentage: Math.round(passedTests/totalTests*100),
            results: this.testResults
        };
    }
}

// 创建全局测试器实例
window.mattingTester = new MattingTester();

// 添加快捷测试命令
window.testMatting = () => window.mattingTester.runAllTests();

console.log('抠图功能测试器已加载。使用 testMatting() 运行测试。');
