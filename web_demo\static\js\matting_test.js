/**
 * 高级背景抠图功能测试脚本
 * 用于验证修复后的抠图功能是否正常工作
 */

class MattingTester {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
        
        console.log('抠图功能测试器初始化');
    }
    
    /**
     * 运行所有测试
     */
    async runAllTests() {
        if (this.isRunning) {
            console.log('测试正在进行中...');
            return;
        }
        
        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 开始运行抠图功能测试...');
        
        try {
            // 基础功能测试
            await this.testBasicFunctionality();
            
            // 事件绑定测试
            await this.testEventBinding();
            
            // 参数调节测试
            await this.testParameterAdjustment();
            
            // 背景替换测试
            await this.testBackgroundReplacement();
            
            // 性能测试
            await this.testPerformance();
            
            // 显示测试结果
            this.showTestResults();
            
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        } finally {
            this.isRunning = false;
        }
    }
    
    /**
     * 基础功能测试
     */
    async testBasicFunctionality() {
        console.log('🔍 测试基础功能...');
        
        const tests = [
            {
                name: '控制器实例存在',
                test: () => typeof window.advancedMattingController !== 'undefined' && window.advancedMattingController !== null
            },
            {
                name: '简化监控器存在',
                test: () => typeof window.simpleCanvasMonitor !== 'undefined' && window.simpleCanvasMonitor !== null
            },
            {
                name: '抠图开关元素存在',
                test: () => document.getElementById('advancedMattingToggle') !== null
            },
            {
                name: '画布元素存在',
                test: () => document.getElementById('canvas_video') !== null
            },
            {
                name: '所有必要模块已加载',
                test: () => {
                    const modules = ['AdvancedGreenScreen', 'TransparentBackground', 'EdgeProcessing', 'BackgroundReplacement', 'AdvancedMattingController'];
                    return modules.every(module => typeof window[module] !== 'undefined');
                }
            }
        ];
        
        for (const test of tests) {
            const result = test.test();
            this.testResults.push({
                category: '基础功能',
                name: test.name,
                passed: result,
                details: result ? '通过' : '失败'
            });
            console.log(`  ${result ? '✅' : '❌'} ${test.name}`);
        }
    }
    
    /**
     * 事件绑定测试
     */
    async testEventBinding() {
        console.log('🔍 测试事件绑定...');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (!toggle) {
            this.testResults.push({
                category: '事件绑定',
                name: '抠图开关事件测试',
                passed: false,
                details: '抠图开关元素不存在'
            });
            return;
        }
        
        // 测试点击事件
        const initialState = window.advancedMattingController ? window.advancedMattingController.enabled : false;
        
        // 模拟点击
        toggle.click();
        
        // 等待状态更新
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const newState = window.advancedMattingController ? window.advancedMattingController.enabled : false;
        const stateChanged = initialState !== newState;
        
        this.testResults.push({
            category: '事件绑定',
            name: '抠图开关点击响应',
            passed: stateChanged,
            details: stateChanged ? `状态从 ${initialState} 变为 ${newState}` : '状态未改变'
        });
        
        console.log(`  ${stateChanged ? '✅' : '❌'} 抠图开关点击响应`);
        
        // 恢复初始状态
        if (stateChanged) {
            toggle.click();
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }
    
    /**
     * 参数调节测试
     */
    async testParameterAdjustment() {
        console.log('🔍 测试参数调节...');
        
        const sliders = [
            'colorToleranceSlider',
            'saturationSlider',
            'brightnessSlider',
            'featherRadiusSlider',
            'antiAliasSlider',
            'smoothingSlider'
        ];
        
        for (const sliderId of sliders) {
            const slider = document.getElementById(sliderId);
            const exists = slider !== null;
            
            let canAdjust = false;
            if (exists) {
                const originalValue = slider.value;
                slider.value = parseFloat(originalValue) + 1;
                
                // 触发input事件
                const event = new Event('input', { bubbles: true });
                slider.dispatchEvent(event);
                
                // 检查值是否改变
                canAdjust = slider.value !== originalValue;
                
                // 恢复原值
                slider.value = originalValue;
                slider.dispatchEvent(event);
            }
            
            this.testResults.push({
                category: '参数调节',
                name: `${sliderId} 调节`,
                passed: exists && canAdjust,
                details: exists ? (canAdjust ? '可调节' : '无法调节') : '元素不存在'
            });
            
            console.log(`  ${exists && canAdjust ? '✅' : '❌'} ${sliderId}`);
        }
    }
    
    /**
     * 背景替换测试
     */
    async testBackgroundReplacement() {
        console.log('🔍 测试背景替换...');
        
        const bgButtons = [
            'transparentBgBtn',
            'colorBgBtn',
            'imageBgBtn'
        ];
        
        for (const buttonId of bgButtons) {
            const button = document.getElementById(buttonId);
            const exists = button !== null;
            
            let canClick = false;
            if (exists) {
                try {
                    button.click();
                    canClick = true;
                } catch (error) {
                    console.warn(`点击 ${buttonId} 时出错:`, error);
                }
            }
            
            this.testResults.push({
                category: '背景替换',
                name: `${buttonId} 按钮`,
                passed: exists && canClick,
                details: exists ? (canClick ? '可点击' : '点击失败') : '元素不存在'
            });
            
            console.log(`  ${exists && canClick ? '✅' : '❌'} ${buttonId}`);
        }
    }
    
    /**
     * 性能测试
     */
    async testPerformance() {
        console.log('🔍 测试性能...');
        
        const controller = window.advancedMattingController;
        if (!controller) {
            this.testResults.push({
                category: '性能',
                name: '性能测试',
                passed: false,
                details: '控制器不存在'
            });
            return;
        }
        
        // 启用抠图功能
        const wasEnabled = controller.enabled;
        if (!wasEnabled) {
            controller.enabled = true;
        }
        
        // 等待几秒钟收集性能数据
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查FPS显示
        const fpsElement = document.getElementById('advancedProcessingFps');
        const hasFpsDisplay = fpsElement && fpsElement.textContent !== '--';
        
        this.testResults.push({
            category: '性能',
            name: 'FPS显示',
            passed: hasFpsDisplay,
            details: hasFpsDisplay ? `FPS: ${fpsElement.textContent}` : '无FPS显示'
        });
        
        console.log(`  ${hasFpsDisplay ? '✅' : '❌'} FPS显示`);
        
        // 恢复原始状态
        if (!wasEnabled) {
            controller.enabled = false;
        }
    }
    
    /**
     * 显示测试结果
     */
    showTestResults() {
        console.log('\n🧪 测试结果总结:');
        console.log('==================');
        
        const categories = [...new Set(this.testResults.map(r => r.category))];
        
        let totalTests = 0;
        let passedTests = 0;
        
        categories.forEach(category => {
            const categoryResults = this.testResults.filter(r => r.category === category);
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            
            console.log(`\n${category}:`);
            categoryResults.forEach(result => {
                console.log(`  ${result.passed ? '✅' : '❌'} ${result.name}: ${result.details}`);
            });
            
            console.log(`  小计: ${categoryPassed}/${categoryResults.length} 通过`);
            
            totalTests += categoryResults.length;
            passedTests += categoryPassed;
        });
        
        console.log('\n==================');
        console.log(`总计: ${passedTests}/${totalTests} 测试通过 (${Math.round(passedTests/totalTests*100)}%)`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有测试通过！抠图功能工作正常。');
        } else {
            console.log('⚠️ 部分测试失败，请检查相关功能。');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            percentage: Math.round(passedTests/totalTests*100),
            results: this.testResults
        };
    }
}

// 创建全局测试器实例
window.mattingTester = new MattingTester();

// 添加快捷测试命令
window.testMatting = () => window.mattingTester.runAllTests();

console.log('抠图功能测试器已加载。使用 testMatting() 运行测试。');
