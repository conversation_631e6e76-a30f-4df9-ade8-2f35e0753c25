/**
 * 抠图功能修复测试脚本
 * 用于验证修复后的抠图功能是否正常工作
 */

class MattingFixTest {
    constructor() {
        this.testResults = [];
        this.fixedIssues = [];
        
        console.log('🧪 抠图修复测试工具初始化');
    }
    
    /**
     * 运行所有修复测试
     */
    async runAllTests() {
        console.log('🧪 开始运行抠图修复测试...');
        this.testResults = [];
        this.fixedIssues = [];
        
        try {
            // 1. 测试DOM元素访问修复
            await this.testDOMElementFixes();
            
            // 2. 测试事件绑定修复
            await this.testEventBindingFixes();
            
            // 3. 测试错误处理修复
            await this.testErrorHandlingFixes();
            
            // 4. 测试模块初始化修复
            await this.testModuleInitializationFixes();
            
            // 5. 测试BackgroundMatting类修复
            await this.testBackgroundMattingFixes();
            
            // 显示测试结果
            this.showTestResults();
            
            return this.testResults;
            
        } catch (error) {
            console.error('❌ 测试过程中发生错误:', error);
            this.addResult('测试执行', '测试运行', false, `测试失败: ${error.message}`);
        }
    }
    
    /**
     * 测试DOM元素访问修复
     */
    async testDOMElementFixes() {
        console.log('🧪 测试DOM元素访问修复...');
        
        // 测试抠图开关元素
        const toggleElement = document.getElementById('advancedMattingToggle');
        const toggleExists = toggleElement !== null;
        
        this.addResult('DOM修复', '抠图开关元素存在', toggleExists, 
            toggleExists ? '元素正常访问' : '元素仍然缺失');
        
        if (toggleExists) {
            this.fixedIssues.push('✅ 修复了抠图开关元素访问问题');
        }
        
        // 测试滑块元素
        const sliders = [
            'precisionSlider',
            'featherSlider',
            'colorToleranceSlider',
            'saturationSlider',
            'brightnessSlider'
        ];
        
        let slidersFound = 0;
        for (const sliderId of sliders) {
            const slider = document.getElementById(sliderId);
            if (slider) {
                slidersFound++;
            }
        }
        
        const slidersOk = slidersFound > 0;
        this.addResult('DOM修复', '滑块元素访问', slidersOk, 
            `找到 ${slidersFound}/${sliders.length} 个滑块元素`);
        
        if (slidersOk) {
            this.fixedIssues.push('✅ 修复了滑块元素访问问题');
        }
        
        // 测试文件上传元素
        const fileInput = document.getElementById('bgFileInput');
        const uploadArea = document.getElementById('uploadArea');
        const uploadElementsOk = fileInput !== null || uploadArea !== null;
        
        this.addResult('DOM修复', '文件上传元素访问', uploadElementsOk, 
            uploadElementsOk ? '上传元素正常访问' : '上传元素仍然缺失');
        
        if (uploadElementsOk) {
            this.fixedIssues.push('✅ 修复了文件上传元素访问问题');
        }
    }
    
    /**
     * 测试事件绑定修复
     */
    async testEventBindingFixes() {
        console.log('🧪 测试事件绑定修复...');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (!toggle) {
            this.addResult('事件绑定修复', '抠图开关事件绑定', false, '开关元素不存在');
            return;
        }
        
        // 检查事件绑定标记
        const hasUnified = toggle.hasAttribute('data-unified-event-bound');
        const hasController = toggle.hasAttribute('data-controller-bound');
        const hasManual = toggle.hasAttribute('data-manual-event-bound');
        
        const hasEventBinding = hasUnified || hasController || hasManual;
        
        this.addResult('事件绑定修复', '事件绑定标记', hasEventBinding, 
            `统一:${hasUnified}, 控制器:${hasController}, 手动:${hasManual}`);
        
        if (hasEventBinding) {
            this.fixedIssues.push('✅ 修复了事件绑定冲突问题');
        }
        
        // 测试点击响应
        try {
            const initialState = window.advancedMattingController ? 
                window.advancedMattingController.enabled : false;
            
            // 模拟点击
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            toggle.dispatchEvent(clickEvent);
            
            // 等待状态更新
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const newState = window.advancedMattingController ? 
                window.advancedMattingController.enabled : false;
            
            const stateChanged = initialState !== newState;
            
            this.addResult('事件绑定修复', '点击响应测试', stateChanged, 
                stateChanged ? '点击事件正常响应' : '点击事件无响应');
            
            if (stateChanged) {
                this.fixedIssues.push('✅ 修复了点击事件响应问题');
                
                // 恢复初始状态
                if (window.advancedMattingController) {
                    window.advancedMattingController.enabled = initialState;
                    toggle.classList.toggle('active', initialState);
                }
            }
            
        } catch (error) {
            this.addResult('事件绑定修复', '点击响应测试', false, `测试失败: ${error.message}`);
        }
    }
    
    /**
     * 测试错误处理修复
     */
    async testErrorHandlingFixes() {
        console.log('🧪 测试错误处理修复...');
        
        // 测试控制器错误处理
        const controller = window.advancedMattingController;
        if (controller) {
            const hasErrorHandling = controller.initializationError !== undefined;
            this.addResult('错误处理修复', '控制器错误处理', hasErrorHandling, 
                hasErrorHandling ? '已添加错误处理机制' : '缺少错误处理');
            
            if (hasErrorHandling) {
                this.fixedIssues.push('✅ 修复了控制器错误处理问题');
            }
        }
        
        // 测试BackgroundMatting错误处理
        if (window.backgroundMatting) {
            const hasErrorHandling = window.backgroundMatting.initializationError !== undefined;
            this.addResult('错误处理修复', 'BackgroundMatting错误处理', hasErrorHandling, 
                hasErrorHandling ? '已添加错误处理机制' : '缺少错误处理');
            
            if (hasErrorHandling) {
                this.fixedIssues.push('✅ 修复了BackgroundMatting错误处理问题');
            }
        }
        
        // 测试集成模块错误处理
        const integration = window.advancedMattingIntegration;
        if (integration) {
            const hasErrorHandling = typeof integration.handleError === 'function';
            this.addResult('错误处理修复', '集成模块错误处理', hasErrorHandling, 
                hasErrorHandling ? '已添加错误处理机制' : '缺少错误处理');
            
            if (hasErrorHandling) {
                this.fixedIssues.push('✅ 修复了集成模块错误处理问题');
            }
        }
    }
    
    /**
     * 测试模块初始化修复
     */
    async testModuleInitializationFixes() {
        console.log('🧪 测试模块初始化修复...');
        
        // 测试延迟初始化
        const controller = window.advancedMattingController;
        if (controller) {
            const hasDelayedInit = controller.isInitialized !== undefined;
            this.addResult('初始化修复', '延迟初始化机制', hasDelayedInit, 
                hasDelayedInit ? '已添加延迟初始化' : '缺少延迟初始化');
            
            if (hasDelayedInit) {
                this.fixedIssues.push('✅ 修复了模块初始化时序问题');
            }
        }
        
        // 测试模块依赖检查
        const modules = [
            { name: 'AdvancedGreenScreen', instance: window.AdvancedGreenScreen },
            { name: 'TransparentBackground', instance: window.TransparentBackground },
            { name: 'EdgeProcessing', instance: window.EdgeProcessing },
            { name: 'BackgroundReplacement', instance: window.BackgroundReplacement }
        ];
        
        let loadedModules = 0;
        for (const module of modules) {
            if (typeof module.instance !== 'undefined') {
                loadedModules++;
            }
        }
        
        const modulesOk = loadedModules === modules.length;
        this.addResult('初始化修复', '模块依赖检查', modulesOk, 
            `已加载 ${loadedModules}/${modules.length} 个模块`);
        
        if (modulesOk) {
            this.fixedIssues.push('✅ 修复了模块依赖问题');
        }
    }
    
    /**
     * 测试BackgroundMatting类修复
     */
    async testBackgroundMattingFixes() {
        console.log('🧪 测试BackgroundMatting类修复...');
        
        // 测试类是否存在
        const classExists = typeof BackgroundMatting !== 'undefined';
        this.addResult('BackgroundMatting修复', '类定义存在', classExists, 
            classExists ? '类定义正常' : '类定义缺失');
        
        if (!classExists) return;
        
        // 测试实例化是否成功
        let instanceCreated = false;
        let instanceError = null;
        
        try {
            const testInstance = new BackgroundMatting();
            instanceCreated = true;
            
            // 测试是否有错误处理
            const hasErrorHandling = testInstance.initializationError !== undefined;
            this.addResult('BackgroundMatting修复', '实例错误处理', hasErrorHandling, 
                hasErrorHandling ? '已添加错误处理' : '缺少错误处理');
            
        } catch (error) {
            instanceError = error;
            console.error('BackgroundMatting实例化测试失败:', error);
        }
        
        this.addResult('BackgroundMatting修复', '实例化测试', instanceCreated, 
            instanceCreated ? '实例化成功' : `实例化失败: ${instanceError?.message}`);
        
        if (instanceCreated) {
            this.fixedIssues.push('✅ 修复了BackgroundMatting实例化问题');
        }
    }
    
    /**
     * 添加测试结果
     */
    addResult(category, name, passed, details) {
        this.testResults.push({
            category,
            name,
            passed,
            details,
            timestamp: new Date().toLocaleTimeString()
        });
    }
    
    /**
     * 显示测试结果
     */
    showTestResults() {
        console.log('\n🧪 抠图修复测试结果:');
        console.log('='.repeat(50));
        
        const categories = [...new Set(this.testResults.map(r => r.category))];
        let totalTests = 0;
        let passedTests = 0;
        
        categories.forEach(category => {
            const categoryResults = this.testResults.filter(r => r.category === category);
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            
            console.log(`\n📋 ${category}:`);
            categoryResults.forEach(result => {
                const icon = result.passed ? '✅' : '❌';
                console.log(`  ${icon} ${result.name}: ${result.details}`);
            });
            
            console.log(`  📊 小计: ${categoryPassed}/${categoryResults.length} 通过`);
            
            totalTests += categoryResults.length;
            passedTests += categoryPassed;
        });
        
        console.log('\n' + '='.repeat(50));
        console.log(`📊 总计: ${passedTests}/${totalTests} 测试通过 (${Math.round(passedTests/totalTests*100)}%)`);
        
        // 显示修复的问题
        if (this.fixedIssues.length > 0) {
            console.log('\n🔧 已修复的问题:');
            this.fixedIssues.forEach(issue => {
                console.log(`  ${issue}`);
            });
        }
        
        if (passedTests === totalTests) {
            console.log('\n🎉 所有修复测试通过！抠图功能应该正常工作。');
        } else {
            console.log('\n⚠️ 仍有部分问题需要进一步修复。');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            percentage: Math.round(passedTests/totalTests*100),
            results: this.testResults,
            fixedIssues: this.fixedIssues
        };
    }
}

// 创建全局实例
window.mattingFixTest = new MattingFixTest();

// 添加快捷命令
window.testMattingFixes = () => window.mattingFixTest.runAllTests();

console.log('🧪 抠图修复测试工具已加载。使用 testMattingFixes() 运行测试。');
