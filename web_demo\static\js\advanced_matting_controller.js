/**
 * 高级背景抠图控制器
 * 整合所有抠图模块并提供统一的控制接口
 */

class AdvancedMattingController {
    constructor() {
        // 核心模块
        this.greenScreen = new AdvancedGreenScreen();
        this.transparentBg = new TransparentBackground();
        this.edgeProcessor = new EdgeProcessing();
        this.bgReplacement = new BackgroundReplacement();
        
        // 控制状态
        this.enabled = false;
        this.currentBackgroundType = 'transparent';
        this.currentQuality = 'medium';
        
        // 性能监控
        this.totalProcessingTime = 0;
        this.frameCount = 0;
        this.lastFpsUpdate = 0;
        this.currentFps = 0;
        
        // 预览设置
        this.enablePreview = true;
        this.previewCanvas = document.createElement('canvas');
        this.previewCtx = this.previewCanvas.getContext('2d');
        
        // 初始化UI
        this.initializeUI();
        
        console.log('高级背景抠图控制器初始化完成');
    }

    /**
     * 状态变化回调
     */
    onStateChange() {
        console.log('抠图状态变化:', {
            enabled: this.enabled,
            backgroundType: this.currentBackgroundType,
            quality: this.currentQuality
        });

        // 更新性能信息显示
        this.updateBackgroundTypeDisplay();

        // 如果启用了抠图，开始性能监控
        if (this.enabled) {
            console.log('抠图功能已启用，开始监控性能...');
        } else {
            console.log('抠图功能已禁用');
        }
    }
    
    /**
     * 初始化用户界面
     */
    initializeUI() {
        console.log('开始初始化高级抠图UI...');

        // 等待DOM元素准备就绪
        const waitForElement = (id, maxAttempts = 50) => {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const checkElement = () => {
                    const element = document.getElementById(id);
                    if (element) {
                        resolve(element);
                    } else if (attempts < maxAttempts) {
                        attempts++;
                        setTimeout(checkElement, 100);
                    } else {
                        reject(new Error(`Element ${id} not found after ${maxAttempts} attempts`));
                    }
                };
                checkElement();
            });
        };

        // 抠图开关 - 使用Promise确保元素存在
        waitForElement('advancedMattingToggle').then(mattingToggle => {
            console.log('找到抠图开关元素，绑定事件...');

            // 检查是否已经绑定过事件
            if (!mattingToggle.hasAttribute('data-controller-bound')) {
                mattingToggle.addEventListener('click', () => {
                    console.log('抠图开关点击事件触发');
                    this.enabled = !this.enabled;
                    mattingToggle.classList.toggle('active', this.enabled);
                    this.updateUI();
                    console.log('高级背景抠图:', this.enabled ? '启用' : '禁用');

                    // 触发状态变化事件
                    this.onStateChange();
                });

                mattingToggle.setAttribute('data-controller-bound', 'true');
                console.log('抠图开关事件绑定成功');
            }
        }).catch(error => {
            console.error('抠图开关元素未找到:', error);
        });
        
        // 延迟初始化滑块（确保DOM元素存在）
        setTimeout(() => {
            console.log('开始初始化滑块控件...');

            // 绿幕参数滑块
            this.initializeSlider('colorToleranceSlider', 'colorToleranceValue', (value) => {
                console.log('色彩容差调节:', value);
                this.greenScreen.setParameters({ colorTolerance: parseInt(value) });
            });

            this.initializeSlider('saturationSlider', 'saturationValue', (value) => {
                console.log('饱和度阈值调节:', value);
                this.greenScreen.setParameters({ saturationMin: parseFloat(value) });
            });

            this.initializeSlider('brightnessSlider', 'brightnessValue', (value) => {
                console.log('明度阈值调节:', value);
                this.greenScreen.setParameters({ valueMin: parseFloat(value) });
            });

            // 边缘处理参数滑块
            this.initializeSlider('featherRadiusSlider', 'featherRadiusValue', (value) => {
                console.log('羽化半径调节:', value);
                this.edgeProcessor.setParameters({ featherRadius: parseInt(value) });
            });

            this.initializeSlider('antiAliasSlider', 'antiAliasValue', (value) => {
                console.log('抗锯齿强度调节:', value);
                this.edgeProcessor.setParameters({ antiAliasStrength: parseFloat(value) });
            });

            this.initializeSlider('smoothingSlider', 'smoothingValue', (value) => {
                console.log('平滑迭代调节:', value);
                this.edgeProcessor.setParameters({ smoothingIterations: parseInt(value) });
            });

            console.log('滑块控件初始化完成');
        }, 200);
        
        // 背景类型按钮
        this.initializeBackgroundTypeButtons();
        
        // 质量控制按钮
        this.initializeQualityButtons();
        
        // 颜色选择器
        this.initializeColorPicker();
        
        // 文件上传
        this.initializeFileUpload();
        
        // 导出和重置按钮
        this.initializeControlButtons();
    }
    
    /**
     * 初始化滑块控件
     */
    initializeSlider(sliderId, valueId, callback) {
        const slider = document.getElementById(sliderId);
        const valueDisplay = document.getElementById(valueId);

        if (slider && valueDisplay) {
            console.log(`初始化滑块: ${sliderId}`);

            slider.addEventListener('input', (e) => {
                const value = e.target.value;
                valueDisplay.textContent = value;

                try {
                    callback(value);
                } catch (error) {
                    console.error(`滑块 ${sliderId} 回调执行失败:`, error);
                }
            });

            // 触发初始值设置
            const initialValue = slider.value;
            valueDisplay.textContent = initialValue;
            try {
                callback(initialValue);
            } catch (error) {
                console.error(`滑块 ${sliderId} 初始值设置失败:`, error);
            }

        } else {
            console.warn(`滑块元素未找到: ${sliderId} 或 ${valueId}`);
        }
    }
    
    /**
     * 初始化背景类型按钮
     */
    initializeBackgroundTypeButtons() {
        const buttons = document.querySelectorAll('[data-type]');
        const colorGroup = document.getElementById('colorInputGroup');
        const uploadArea = document.getElementById('advancedUploadArea');
        
        buttons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新背景类型
                this.currentBackgroundType = btn.dataset.type;
                this.bgReplacement.setBackground(this.currentBackgroundType, null);
                
                // 显示/隐藏相关控件
                if (colorGroup) colorGroup.style.display = this.currentBackgroundType === 'color' ? 'flex' : 'none';
                if (uploadArea) uploadArea.style.display = this.currentBackgroundType === 'image' ? 'block' : 'none';
                
                // 更新性能信息显示
                this.updateBackgroundTypeDisplay();
                
                console.log('背景类型切换到:', this.currentBackgroundType);
            });
        });
    }
    
    /**
     * 初始化质量控制按钮
     */
    initializeQualityButtons() {
        const buttons = document.querySelectorAll('[data-quality]');
        
        buttons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 更新质量等级
                this.currentQuality = btn.dataset.quality;
                this.greenScreen.adjustQuality(this.currentQuality);
                
                // 更新边缘处理参数
                this.adjustEdgeProcessingQuality(this.currentQuality);
                
                console.log('质量等级切换到:', this.currentQuality);
            });
        });
    }
    
    /**
     * 根据质量等级调整边缘处理参数
     */
    adjustEdgeProcessingQuality(quality) {
        switch (quality) {
            case 'low':
                this.edgeProcessor.setParameters({
                    enableGradientFeather: false,
                    enableEdgeDetection: false,
                    enableSubpixelSmoothing: false,
                    smoothingIterations: 1
                });
                break;
                
            case 'medium':
                this.edgeProcessor.setParameters({
                    enableGradientFeather: true,
                    enableEdgeDetection: true,
                    enableSubpixelSmoothing: false,
                    smoothingIterations: 2
                });
                break;
                
            case 'high':
                this.edgeProcessor.setParameters({
                    enableGradientFeather: true,
                    enableEdgeDetection: true,
                    enableSubpixelSmoothing: true,
                    smoothingIterations: 3
                });
                break;
        }
    }
    
    /**
     * 初始化颜色选择器
     */
    initializeColorPicker() {
        const colorPicker = document.getElementById('bgColorPicker');
        const hexInput = document.getElementById('bgHexInput');
        
        if (colorPicker) {
            colorPicker.addEventListener('input', (e) => {
                const color = e.target.value;
                this.bgReplacement.setBackground('color', color);
                if (hexInput) hexInput.value = color.toUpperCase();
            });
        }
        
        if (hexInput) {
            hexInput.addEventListener('input', (e) => {
                let value = e.target.value.trim();
                
                // 自动添加#号
                if (value && !value.startsWith('#')) {
                    value = '#' + value;
                    e.target.value = value;
                }
                
                // 验证HEX格式
                if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)) {
                    this.bgReplacement.setBackground('color', value);
                    if (colorPicker) colorPicker.value = value;
                }
            });
        }
    }
    
    /**
     * 初始化文件上传
     */
    initializeFileUpload() {
        const fileInput = document.getElementById('advancedBgFileInput');
        const uploadArea = document.getElementById('advancedUploadArea');
        
        if (uploadArea && fileInput) {
            // 点击上传
            uploadArea.addEventListener('click', () => fileInput.click());
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFileUpload(files[0]);
                }
            });
            
            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleFileUpload(e.target.files[0]);
                }
            });
        }
    }
    
    /**
     * 处理文件上传
     */
    handleFileUpload(file) {
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件 (JPG, PNG, WebP)');
            return;
        }
        
        console.log('上传背景图片:', file.name, file.size, 'bytes');
        
        // 更新上传区域显示
        const uploadArea = document.getElementById('advancedUploadArea');
        if (uploadArea) {
            const uploadText = uploadArea.querySelector('.upload-text');
            if (uploadText) {
                uploadText.textContent = `📷 ${file.name}`;
            }
        }
        
        // 设置背景图片
        this.bgReplacement.setBackground('image', file);
    }
    
    /**
     * 初始化控制按钮
     */
    initializeControlButtons() {
        // 导出PNG按钮
        const exportBtn = document.getElementById('exportPngBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportToPNG();
            });
        }
        
        // 重置按钮
        const resetBtn = document.getElementById('resetMattingBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetAllSettings();
            });
        }
    }
    
    /**
     * 主要处理方法
     * @param {ImageData} imageData - 输入图像数据
     * @param {number} width - 图像宽度
     * @param {number} height - 图像高度
     * @returns {ImageData} 处理后的图像数据
     */
    async process(imageData, width, height) {
        if (!this.enabled) return imageData;
        
        const startTime = performance.now();
        
        try {
            // 第一步：绿幕抠图
            const greenScreenResult = this.greenScreen.process(imageData, width, height);
            
            // 第二步：边缘处理
            const processedMask = this.edgeProcessor.processEdges(
                greenScreenResult.maskData, width, height
            );
            
            // 第三步：背景合成或透明输出
            let finalResult;
            if (this.currentBackgroundType === 'transparent') {
                finalResult = this.transparentBg.processTransparentBackground(
                    greenScreenResult.imageData, processedMask, width, height
                );
            } else {
                finalResult = this.bgReplacement.composite(
                    greenScreenResult.imageData, processedMask, width, height
                );
            }
            
            // 性能统计
            const processingTime = performance.now() - startTime;
            this.updatePerformanceStats(processingTime);
            
            return finalResult;
            
        } catch (error) {
            console.error('高级背景抠图处理失败:', error);
            return imageData;
        }
    }
    
    /**
     * 更新性能统计
     */
    updatePerformanceStats(processingTime) {
        this.totalProcessingTime += processingTime;
        this.frameCount++;
        
        const now = performance.now();
        if (now - this.lastFpsUpdate > 1000) { // 每秒更新一次
            this.currentFps = Math.round(1000 / (this.totalProcessingTime / this.frameCount));
            this.updateFpsDisplay();
            
            // 重置统计
            this.totalProcessingTime = 0;
            this.frameCount = 0;
            this.lastFpsUpdate = now;
        }
    }
    
    /**
     * 更新FPS显示
     */
    updateFpsDisplay() {
        const fpsElement = document.getElementById('advancedProcessingFps');
        if (fpsElement) {
            fpsElement.textContent = this.currentFps;
            
            // 根据FPS设置颜色
            if (this.currentFps >= 15) {
                fpsElement.style.color = '#4cc9f0';
            } else if (this.currentFps >= 10) {
                fpsElement.style.color = '#ffa500';
            } else {
                fpsElement.style.color = '#ff4757';
            }
        }
        
        // 更新边缘质量显示
        const edgeQualityElement = document.getElementById('edgeQuality');
        if (edgeQualityElement) {
            const qualityMap = { 'low': '低', 'medium': '中等', 'high': '高' };
            edgeQualityElement.textContent = qualityMap[this.currentQuality] || '中等';
        }
    }
    
    /**
     * 更新背景类型显示
     */
    updateBackgroundTypeDisplay() {
        const bgTypeElement = document.getElementById('currentBgType');
        if (bgTypeElement) {
            const typeMap = {
                'transparent': '透明',
                'color': '纯色',
                'image': '图片'
            };
            bgTypeElement.textContent = typeMap[this.currentBackgroundType] || '透明';
        }
    }
    
    /**
     * 导出为PNG
     */
    exportToPNG() {
        if (this.currentBackgroundType === 'transparent') {
            const filename = `transparent_output_${Date.now()}.png`;
            this.transparentBg.downloadImage(filename);
            console.log('导出透明背景PNG:', filename);
        } else {
            const filename = `background_replaced_${Date.now()}.png`;
            const canvas = this.bgReplacement.getCompositeCanvas();
            const dataURL = canvas.toDataURL('image/png', 0.9);
            
            const link = document.createElement('a');
            link.download = filename;
            link.href = dataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            console.log('导出背景替换PNG:', filename);
        }
    }
    
    /**
     * 重置所有设置
     */
    resetAllSettings() {
        // 重置绿幕参数
        this.greenScreen.setParameters({
            hueRange: [60, 180],
            saturationMin: 0.3,
            valueMin: 0.2,
            colorTolerance: 20,
            edgeFeather: 3,
            spill: 0.1
        });
        
        // 重置边缘处理参数
        this.edgeProcessor.setParameters({
            featherRadius: 3,
            antiAliasStrength: 0.5,
            smoothingIterations: 2
        });
        
        // 重置UI滑块
        this.resetSliderValues();
        
        // 重置背景类型
        this.currentBackgroundType = 'transparent';
        this.currentQuality = 'medium';
        
        // 更新UI
        this.updateUI();
        
        console.log('所有设置已重置');
    }
    
    /**
     * 重置滑块值
     */
    resetSliderValues() {
        const sliders = [
            { id: 'colorToleranceSlider', value: 20, displayId: 'colorToleranceValue' },
            { id: 'saturationSlider', value: 0.3, displayId: 'saturationValue' },
            { id: 'brightnessSlider', value: 0.2, displayId: 'brightnessValue' },
            { id: 'featherRadiusSlider', value: 3, displayId: 'featherRadiusValue' },
            { id: 'antiAliasSlider', value: 0.5, displayId: 'antiAliasValue' },
            { id: 'smoothingSlider', value: 2, displayId: 'smoothingValue' }
        ];
        
        sliders.forEach(({ id, value, displayId }) => {
            const slider = document.getElementById(id);
            const display = document.getElementById(displayId);
            
            if (slider) slider.value = value;
            if (display) display.textContent = value;
        });
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新背景类型按钮
        document.querySelectorAll('[data-type]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === this.currentBackgroundType);
        });
        
        // 更新质量按钮
        document.querySelectorAll('[data-quality]').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.quality === this.currentQuality);
        });
        
        // 更新显示
        this.updateBackgroundTypeDisplay();
    }
    
    /**
     * 获取当前设置
     */
    getSettings() {
        return {
            enabled: this.enabled,
            backgroundType: this.currentBackgroundType,
            quality: this.currentQuality,
            greenScreenParams: this.greenScreen.getParameters(),
            edgeProcessingParams: this.edgeProcessor.getParameters ? this.edgeProcessor.getParameters() : {},
            performance: {
                fps: this.currentFps,
                frameCount: this.frameCount
            }
        };
    }
}

// 导出模块
window.AdvancedMattingController = AdvancedMattingController;
