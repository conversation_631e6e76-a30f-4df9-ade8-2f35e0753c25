<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>背景抠图功能修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #4361ee;
            border-radius: 10px;
            background: #f8f9fa;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4361ee;
        }
        
        .test-button {
            background: #4361ee;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        
        .test-button:hover {
            background: #3f37c9;
            transform: translateY(-2px);
        }
        
        .test-button.success {
            background: #52b788;
        }
        
        .test-button.warning {
            background: #f77f00;
        }
        
        .test-button.danger {
            background: #d62828;
        }
        
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background-color: #52b788; }
        .status-warning { background-color: #f77f00; }
        .status-error { background-color: #d62828; }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background-color: #ccc;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: inline-block;
            margin: 10px;
        }
        
        .toggle-switch.active {
            background-color: #4361ee;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(25px);
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 5px solid #4361ee;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 背景抠图功能修复测试</h1>
        
        <!-- 模块加载测试 -->
        <div class="test-section">
            <h3>📦 模块加载状态</h3>
            <p>检查所有必要的JavaScript模块是否正确加载：</p>
            <button class="test-button" onclick="checkModules()">检查模块加载</button>
            <div id="moduleResults" class="test-results" style="display: none;"></div>
        </div>
        
        <!-- 抠图开关测试 -->
        <div class="test-section">
            <h3>🎛️ 抠图开关测试</h3>
            <p>测试抠图开关的响应性：</p>
            
            <!-- 模拟的抠图开关 -->
            <div>
                <label>测试开关：</label>
                <div class="toggle-switch" id="testToggle">
                    <div class="toggle-slider"></div>
                </div>
                <span id="toggleStatus">关闭</span>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="test-button" onclick="testToggleFunction()">测试开关功能</button>
                <button class="test-button success" onclick="bindTestToggle()">绑定测试事件</button>
            </div>
            
            <div id="toggleResults" class="test-results" style="display: none;"></div>
        </div>
        
        <!-- 控制器实例测试 -->
        <div class="test-section">
            <h3>🎮 控制器实例测试</h3>
            <p>检查AdvancedMattingController实例状态：</p>
            <button class="test-button" onclick="checkController()">检查控制器</button>
            <button class="test-button warning" onclick="createController()">创建控制器</button>
            <button class="test-button success" onclick="testControllerMethods()">测试控制器方法</button>
            <div id="controllerResults" class="test-results" style="display: none;"></div>
        </div>
        
        <!-- 完整诊断 -->
        <div class="test-section">
            <h3>🔍 完整系统诊断</h3>
            <p>运行完整的系统诊断和自动修复：</p>
            <button class="test-button" onclick="runDiagnosis()">运行诊断</button>
            <button class="test-button warning" onclick="attemptFix()">尝试修复</button>
            <button class="test-button danger" onclick="clearConsole()">清空控制台</button>
            
            <div class="console-output" id="consoleOutput">
点击"运行诊断"开始检查系统状态...
            </div>
        </div>
        
        <!-- 实际页面测试 -->
        <div class="test-section">
            <h3>🚀 实际页面测试</h3>
            <p>在实际的数字人页面中测试修复效果：</p>
            <button class="test-button success" onclick="openMainPage()">打开数字人页面</button>
            <button class="test-button" onclick="showInstructions()">查看测试说明</button>
        </div>
    </div>

    <!-- 引入必要的模块 -->
    <script src="js/advanced_green_screen.js"></script>
    <script src="js/transparent_background.js"></script>
    <script src="js/edge_processing.js"></script>
    <script src="js/background_replacement.js"></script>
    <script src="js/advanced_matting_controller.js"></script>
    <script src="js/matting_debugger.js"></script>

    <script>
        let testController = null;
        let consoleOutput = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            consoleOutput = document.getElementById('consoleOutput');
            log('测试页面加载完成');
            
            // 延迟检查模块
            setTimeout(() => {
                log('开始自动检查模块加载状态...');
                checkModules();
            }, 1000);
        });
        
        // 日志输出函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            console.log(logMessage);
            
            if (consoleOutput) {
                consoleOutput.textContent += logMessage + '\n';
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            }
        }
        
        // 检查模块加载状态
        function checkModules() {
            const modules = [
                'AdvancedGreenScreen',
                'TransparentBackground',
                'EdgeProcessing', 
                'BackgroundReplacement',
                'AdvancedMattingController',
                'MattingDebugger'
            ];
            
            log('=== 检查模块加载状态 ===');
            
            const results = document.getElementById('moduleResults');
            let html = '<h4>模块加载结果：</h4>';
            let allLoaded = true;
            
            modules.forEach(moduleName => {
                const isLoaded = typeof window[moduleName] !== 'undefined';
                const status = isLoaded ? 'success' : 'error';
                const icon = isLoaded ? '✅' : '❌';
                
                html += `<div><span class="status-indicator status-${status}"></span>${icon} ${moduleName}</div>`;
                log(`${moduleName}: ${isLoaded ? '已加载' : '未加载'}`);
                
                if (!isLoaded) allLoaded = false;
            });
            
            html += `<div style="margin-top: 15px;"><strong>总体状态: ${allLoaded ? '✅ 所有模块已加载' : '❌ 部分模块未加载'}</strong></div>`;
            
            results.innerHTML = html;
            results.style.display = 'block';
            
            log(`模块检查完成，状态: ${allLoaded ? '正常' : '异常'}`);
        }
        
        // 绑定测试开关事件
        function bindTestToggle() {
            const testToggle = document.getElementById('testToggle');
            const toggleStatus = document.getElementById('toggleStatus');
            
            if (testToggle) {
                // 清除之前的事件监听器
                testToggle.replaceWith(testToggle.cloneNode(true));
                const newToggle = document.getElementById('testToggle');
                
                let isActive = false;
                
                newToggle.addEventListener('click', function() {
                    isActive = !isActive;
                    newToggle.classList.toggle('active', isActive);
                    toggleStatus.textContent = isActive ? '开启' : '关闭';
                    log(`测试开关状态变更: ${isActive ? '开启' : '关闭'}`);
                });
                
                log('测试开关事件绑定成功');
            }
        }
        
        // 测试开关功能
        function testToggleFunction() {
            const testToggle = document.getElementById('testToggle');
            const results = document.getElementById('toggleResults');
            
            if (testToggle) {
                log('开始测试开关功能...');
                
                // 模拟点击
                const clickEvent = new Event('click');
                testToggle.dispatchEvent(clickEvent);
                
                setTimeout(() => {
                    const isActive = testToggle.classList.contains('active');
                    const html = `
                        <h4>开关测试结果：</h4>
                        <div><span class="status-indicator status-${isActive ? 'success' : 'warning'}"></span>
                        开关状态: ${isActive ? '激活' : '未激活'}</div>
                        <div>事件响应: ${isActive ? '✅ 正常' : '⚠️ 可能有问题'}</div>
                    `;
                    
                    results.innerHTML = html;
                    results.style.display = 'block';
                    
                    log(`开关测试完成，状态: ${isActive ? '正常' : '异常'}`);
                }, 100);
            }
        }
        
        // 检查控制器
        function checkController() {
            log('=== 检查控制器实例 ===');
            
            const controller = window.advancedMattingController;
            const results = document.getElementById('controllerResults');
            
            let html = '<h4>控制器检查结果：</h4>';
            
            if (controller) {
                html += `<div><span class="status-indicator status-success"></span>✅ 控制器实例存在</div>`;
                html += `<div>启用状态: ${controller.enabled ? '✅ 已启用' : '❌ 未启用'}</div>`;
                html += `<div>背景类型: ${controller.currentBackgroundType || 'undefined'}</div>`;
                html += `<div>质量等级: ${controller.currentQuality || 'undefined'}</div>`;
                
                log('控制器实例存在');
                log(`启用状态: ${controller.enabled}`);
                log(`背景类型: ${controller.currentBackgroundType}`);
                
            } else {
                html += `<div><span class="status-indicator status-error"></span>❌ 控制器实例不存在</div>`;
                log('控制器实例不存在');
            }
            
            results.innerHTML = html;
            results.style.display = 'block';
        }
        
        // 创建控制器
        function createController() {
            log('尝试创建控制器实例...');
            
            try {
                if (typeof AdvancedMattingController !== 'undefined') {
                    testController = new AdvancedMattingController();
                    window.advancedMattingController = testController;
                    log('✅ 控制器创建成功');
                    
                    // 重新检查
                    setTimeout(checkController, 500);
                } else {
                    log('❌ AdvancedMattingController 类未定义');
                }
            } catch (error) {
                log(`❌ 控制器创建失败: ${error.message}`);
                console.error('控制器创建错误:', error);
            }
        }
        
        // 测试控制器方法
        function testControllerMethods() {
            const controller = window.advancedMattingController || testController;
            
            if (!controller) {
                log('❌ 没有可用的控制器实例');
                return;
            }
            
            log('=== 测试控制器方法 ===');
            
            try {
                // 测试基本方法
                const settings = controller.getSettings();
                log('✅ getSettings() 方法正常');
                log(`设置信息: ${JSON.stringify(settings, null, 2)}`);
                
                // 测试状态切换
                const originalState = controller.enabled;
                controller.enabled = !originalState;
                log(`✅ 状态切换测试: ${originalState} -> ${controller.enabled}`);
                
                // 恢复原状态
                controller.enabled = originalState;
                
                log('✅ 控制器方法测试完成');
                
            } catch (error) {
                log(`❌ 控制器方法测试失败: ${error.message}`);
                console.error('控制器方法测试错误:', error);
            }
        }
        
        // 运行完整诊断
        function runDiagnosis() {
            log('🔍 开始运行完整诊断...');
            
            if (typeof window.mattingDebugger !== 'undefined') {
                const diagnosis = window.mattingDebugger.runFullDiagnosis();
                
                log('=== 诊断结果 ===');
                if (diagnosis.issues.length > 0) {
                    log('发现问题:');
                    diagnosis.issues.forEach(issue => log(`  - ${issue}`));
                } else {
                    log('✅ 未发现明显问题');
                }
                
            } else {
                log('❌ 调试器不可用');
            }
        }
        
        // 尝试修复
        function attemptFix() {
            log('🔧 尝试自动修复...');
            
            if (typeof window.mattingDebugger !== 'undefined') {
                const fixes = window.mattingDebugger.attemptFix();
                
                if (fixes.length > 0) {
                    log('应用的修复:');
                    fixes.forEach(fix => log(`  - ${fix}`));
                } else {
                    log('没有可应用的修复');
                }
                
                // 重新运行诊断
                setTimeout(runDiagnosis, 1000);
                
            } else {
                log('❌ 调试器不可用，尝试手动修复...');
                
                // 手动修复尝试
                try {
                    // 重新创建控制器
                    if (!window.advancedMattingController && typeof AdvancedMattingController !== 'undefined') {
                        window.advancedMattingController = new AdvancedMattingController();
                        log('✅ 手动创建控制器成功');
                    }
                } catch (error) {
                    log(`❌ 手动修复失败: ${error.message}`);
                }
            }
        }
        
        // 清空控制台
        function clearConsole() {
            if (consoleOutput) {
                consoleOutput.textContent = '控制台已清空...\n';
            }
        }
        
        // 打开主页面
        function openMainPage() {
            window.open('MiniLive_RealTime.html', '_blank');
            log('已打开数字人主页面');
        }
        
        // 显示测试说明
        function showInstructions() {
            const instructions = `
=== 测试说明 ===

1. 首先在此页面运行完整诊断，确保所有模块正常加载
2. 如果发现问题，点击"尝试修复"
3. 打开数字人主页面 (MiniLive_RealTime.html)
4. 在控制面板中找到"高级背景抠图"区域
5. 点击"启用抠图"开关，观察是否有响应
6. 如果仍无响应，在浏览器控制台运行以下命令：
   - debugMatting() - 运行诊断
   - fixMatting() - 尝试修复
7. 调节各项参数，测试功能是否正常

常见问题排查：
- 检查浏览器控制台是否有JavaScript错误
- 确认所有JS文件都已正确加载
- 验证DOM元素ID是否正确
- 检查事件监听器是否正确绑定

如果问题仍然存在，请查看浏览器开发者工具的控制台输出。
            `;
            
            log(instructions);
        }
        
        // 自动绑定测试开关
        setTimeout(bindTestToggle, 1000);
    </script>
</body>
</html>
