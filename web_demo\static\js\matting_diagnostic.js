/**
 * 高级背景抠图诊断和修复工具
 * 专门用于诊断和修复抠图功能的问题
 */

class MattingDiagnostic {
    constructor() {
        this.diagnosticResults = [];
        this.fixAttempts = [];
        
        console.log('🔧 抠图诊断工具初始化');
    }
    
    /**
     * 运行完整诊断
     */
    async runFullDiagnosis() {
        console.log('🔍 开始运行抠图功能完整诊断...');
        this.diagnosticResults = [];
        
        try {
            // 1. 检查DOM元素
            await this.checkDOMElements();
            
            // 2. 检查模块加载
            await this.checkModuleLoading();
            
            // 3. 检查事件绑定
            await this.checkEventBinding();
            
            // 4. 检查控制器状态
            await this.checkControllerState();
            
            // 5. 检查集成模块
            await this.checkIntegrationModules();
            
            // 6. 检查视频流
            await this.checkVideoStream();
            
            // 显示诊断结果
            this.showDiagnosticResults();
            
            return this.diagnosticResults;
            
        } catch (error) {
            console.error('❌ 诊断过程中发生错误:', error);
            this.addResult('诊断过程', '诊断执行', false, `诊断失败: ${error.message}`);
        }
    }
    
    /**
     * 检查DOM元素
     */
    async checkDOMElements() {
        console.log('🔍 检查DOM元素...');
        
        const elements = [
            { id: 'advancedMattingToggle', name: '抠图开关' },
            { id: 'canvas_video', name: '视频画布' },
            { id: 'colorToleranceSlider', name: '色彩容差滑块' },
            { id: 'transparentBgBtn', name: '透明背景按钮' },
            { id: 'advancedProcessingFps', name: 'FPS显示' }
        ];
        
        for (const element of elements) {
            const el = document.getElementById(element.id);
            const exists = el !== null;
            
            this.addResult('DOM元素', element.name, exists, 
                exists ? '元素存在' : '元素不存在');
            
            if (exists && element.id === 'advancedMattingToggle') {
                // 检查事件绑定标记
                const hasUnified = el.hasAttribute('data-unified-event-bound');
                const hasController = el.hasAttribute('data-controller-bound');
                const hasManual = el.hasAttribute('data-manual-event-bound');
                
                this.addResult('DOM元素', '抠图开关事件绑定标记', 
                    hasUnified || hasController || hasManual,
                    `统一:${hasUnified}, 控制器:${hasController}, 手动:${hasManual}`);
            }
        }
    }
    
    /**
     * 检查模块加载
     */
    async checkModuleLoading() {
        console.log('🔍 检查模块加载...');
        
        const modules = [
            { name: 'AdvancedGreenScreen', class: window.AdvancedGreenScreen },
            { name: 'TransparentBackground', class: window.TransparentBackground },
            { name: 'EdgeProcessing', class: window.EdgeProcessing },
            { name: 'BackgroundReplacement', class: window.BackgroundReplacement },
            { name: 'AdvancedMattingController', class: window.AdvancedMattingController }
        ];
        
        for (const module of modules) {
            const loaded = typeof module.class !== 'undefined';
            this.addResult('模块加载', module.name, loaded, 
                loaded ? '模块已加载' : '模块未加载');
        }
        
        // 检查实例
        const controller = window.advancedMattingController;
        const integration = window.advancedMattingIntegration;
        const monitor = window.simpleCanvasMonitor;
        
        this.addResult('模块实例', '抠图控制器实例', !!controller, 
            controller ? '实例存在' : '实例不存在');
        this.addResult('模块实例', '集成模块实例', !!integration, 
            integration ? '实例存在' : '实例不存在');
        this.addResult('模块实例', '简化监控器实例', !!monitor, 
            monitor ? '实例存在' : '实例不存在');
    }
    
    /**
     * 检查事件绑定
     */
    async checkEventBinding() {
        console.log('🔍 检查事件绑定...');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (!toggle) {
            this.addResult('事件绑定', '抠图开关元素', false, '元素不存在');
            return;
        }
        
        // 模拟点击测试
        try {
            const initialState = window.advancedMattingController ? 
                window.advancedMattingController.enabled : false;
            
            // 创建点击事件
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            toggle.dispatchEvent(clickEvent);
            
            // 等待状态更新
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const newState = window.advancedMattingController ? 
                window.advancedMattingController.enabled : false;
            
            const stateChanged = initialState !== newState;
            
            this.addResult('事件绑定', '点击响应测试', stateChanged, 
                stateChanged ? `状态从${initialState}变为${newState}` : '状态未改变');
            
            // 恢复初始状态
            if (stateChanged && window.advancedMattingController) {
                window.advancedMattingController.enabled = initialState;
                toggle.classList.toggle('active', initialState);
            }
            
        } catch (error) {
            this.addResult('事件绑定', '点击响应测试', false, `测试失败: ${error.message}`);
        }
    }
    
    /**
     * 检查控制器状态
     */
    async checkControllerState() {
        console.log('🔍 检查控制器状态...');
        
        const controller = window.advancedMattingController;
        if (!controller) {
            this.addResult('控制器状态', '控制器存在性', false, '控制器不存在');
            return;
        }
        
        // 检查子模块
        const subModules = [
            { name: 'greenScreen', module: controller.greenScreen },
            { name: 'transparentBg', module: controller.transparentBg },
            { name: 'edgeProcessor', module: controller.edgeProcessor },
            { name: 'bgReplacement', module: controller.bgReplacement }
        ];
        
        for (const subModule of subModules) {
            const exists = !!subModule.module;
            this.addResult('控制器状态', `${subModule.name}模块`, exists, 
                exists ? '模块存在' : '模块不存在');
        }
        
        // 检查状态
        this.addResult('控制器状态', '启用状态', true, 
            `当前状态: ${controller.enabled ? '启用' : '禁用'}`);
        this.addResult('控制器状态', '背景类型', true, 
            `当前类型: ${controller.currentBackgroundType}`);
        this.addResult('控制器状态', '质量等级', true, 
            `当前质量: ${controller.currentQuality}`);
    }
    
    /**
     * 检查集成模块
     */
    async checkIntegrationModules() {
        console.log('🔍 检查集成模块...');
        
        const integration = window.advancedMattingIntegration;
        const monitor = window.simpleCanvasMonitor;
        
        if (integration) {
            this.addResult('集成模块', '完整集成模块', true, 
                `集成状态: ${integration.isIntegrated ? '已集成' : '未集成'}`);
            this.addResult('集成模块', '处理启用状态', true, 
                `处理状态: ${integration.processingEnabled ? '启用' : '禁用'}`);
        } else {
            this.addResult('集成模块', '完整集成模块', false, '模块不存在');
        }
        
        if (monitor) {
            this.addResult('集成模块', '简化监控器', true, 
                `监控状态: ${monitor.enabled ? '启用' : '禁用'}`);
            this.addResult('集成模块', '监控器控制器', !!monitor.controller, 
                monitor.controller ? '控制器已设置' : '控制器未设置');
        } else {
            this.addResult('集成模块', '简化监控器', false, '模块不存在');
        }
    }
    
    /**
     * 检查视频流
     */
    async checkVideoStream() {
        console.log('🔍 检查视频流...');
        
        // 检查全局变量
        const globals = [
            { name: 'canvas_video', value: window.canvas_video },
            { name: 'ctx_video', value: window.ctx_video },
            { name: 'processVideoFrames', value: window.processVideoFrames },
            { name: 'frameIndex', value: window.frameIndex }
        ];
        
        for (const global of globals) {
            const exists = typeof global.value !== 'undefined' && global.value !== null;
            this.addResult('视频流', global.name, exists, 
                exists ? '变量存在' : '变量不存在');
        }
        
        // 检查画布状态
        if (window.canvas_video) {
            const canvas = window.canvas_video;
            const hasValidSize = canvas.width > 0 && canvas.height > 0;
            this.addResult('视频流', '画布尺寸', hasValidSize, 
                `尺寸: ${canvas.width}x${canvas.height}`);
        }
    }
    
    /**
     * 添加诊断结果
     */
    addResult(category, name, passed, details) {
        this.diagnosticResults.push({
            category,
            name,
            passed,
            details,
            timestamp: new Date().toLocaleTimeString()
        });
    }
    
    /**
     * 显示诊断结果
     */
    showDiagnosticResults() {
        console.log('\n🔍 抠图功能诊断结果:');
        console.log('='.repeat(50));
        
        const categories = [...new Set(this.diagnosticResults.map(r => r.category))];
        let totalTests = 0;
        let passedTests = 0;
        
        categories.forEach(category => {
            const categoryResults = this.diagnosticResults.filter(r => r.category === category);
            const categoryPassed = categoryResults.filter(r => r.passed).length;
            
            console.log(`\n📋 ${category}:`);
            categoryResults.forEach(result => {
                const icon = result.passed ? '✅' : '❌';
                console.log(`  ${icon} ${result.name}: ${result.details}`);
            });
            
            console.log(`  📊 小计: ${categoryPassed}/${categoryResults.length} 通过`);
            
            totalTests += categoryResults.length;
            passedTests += categoryPassed;
        });
        
        console.log('\n' + '='.repeat(50));
        console.log(`📊 总计: ${passedTests}/${totalTests} 测试通过 (${Math.round(passedTests/totalTests*100)}%)`);
        
        if (passedTests === totalTests) {
            console.log('🎉 所有诊断项目通过！抠图功能应该正常工作。');
        } else {
            console.log('⚠️ 发现问题，建议运行 fixMatting() 尝试自动修复。');
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            percentage: Math.round(passedTests/totalTests*100),
            results: this.diagnosticResults
        };
    }
    
    /**
     * 尝试自动修复
     */
    async attemptAutoFix() {
        console.log('🔧 开始尝试自动修复抠图功能...');
        this.fixAttempts = [];
        
        try {
            // 1. 修复事件绑定
            await this.fixEventBinding();
            
            // 2. 修复模块初始化
            await this.fixModuleInitialization();
            
            // 3. 修复集成状态
            await this.fixIntegrationState();
            
            console.log('🔧 自动修复完成，建议重新运行诊断验证结果');
            
        } catch (error) {
            console.error('❌ 自动修复过程中发生错误:', error);
        }
    }
    
    /**
     * 修复事件绑定
     */
    async fixEventBinding() {
        console.log('🔧 修复事件绑定...');
        
        const toggle = document.getElementById('advancedMattingToggle');
        if (!toggle) {
            this.fixAttempts.push({ action: '修复事件绑定', success: false, details: '抠图开关元素不存在' });
            return;
        }
        
        try {
            // 清除所有事件绑定标记
            toggle.removeAttribute('data-controller-bound');
            toggle.removeAttribute('data-manual-event-bound');
            toggle.removeAttribute('data-unified-event-bound');
            
            // 重新创建元素以清除所有事件监听器
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);
            
            this.fixAttempts.push({ action: '清除旧事件绑定', success: true, details: '已清除所有旧的事件绑定' });
            
            // 触发重新初始化
            if (window.advancedMattingController && window.advancedMattingController.initializeUI) {
                setTimeout(() => {
                    window.advancedMattingController.initializeUI();
                }, 100);
            }
            
            this.fixAttempts.push({ action: '重新绑定事件', success: true, details: '已触发事件重新绑定' });
            
        } catch (error) {
            this.fixAttempts.push({ action: '修复事件绑定', success: false, details: error.message });
        }
    }
    
    /**
     * 修复模块初始化
     */
    async fixModuleInitialization() {
        console.log('🔧 修复模块初始化...');
        
        try {
            // 重新初始化控制器
            if (typeof AdvancedMattingController !== 'undefined' && !window.advancedMattingController) {
                window.advancedMattingController = new AdvancedMattingController();
                this.fixAttempts.push({ action: '重新创建控制器', success: true, details: '控制器实例已重新创建' });
            }
            
            // 重新初始化集成模块
            if (window.advancedMattingController && !window.simpleCanvasMonitor) {
                const SimpleCanvasMonitor = window.SimpleCanvasMonitor;
                if (SimpleCanvasMonitor) {
                    window.simpleCanvasMonitor = new SimpleCanvasMonitor();
                    window.simpleCanvasMonitor.setController(window.advancedMattingController);
                    this.fixAttempts.push({ action: '重新创建监控器', success: true, details: '简化监控器已重新创建' });
                }
            }
            
        } catch (error) {
            this.fixAttempts.push({ action: '修复模块初始化', success: false, details: error.message });
        }
    }
    
    /**
     * 修复集成状态
     */
    async fixIntegrationState() {
        console.log('🔧 修复集成状态...');
        
        try {
            // 确保集成模块正确设置
            if (window.advancedMattingController && window.simpleCanvasMonitor) {
                window.simpleCanvasMonitor.setController(window.advancedMattingController);
                this.fixAttempts.push({ action: '设置监控器控制器', success: true, details: '监控器控制器已设置' });
            }
            
            // 重置错误状态
            if (window.advancedMattingIntegration) {
                window.advancedMattingIntegration.errorCount = 0;
                window.advancedMattingIntegration.processingEnabled = true;
                this.fixAttempts.push({ action: '重置集成状态', success: true, details: '集成模块状态已重置' });
            }
            
        } catch (error) {
            this.fixAttempts.push({ action: '修复集成状态', success: false, details: error.message });
        }
    }
}

// 创建全局实例
window.mattingDiagnostic = new MattingDiagnostic();

// 添加快捷命令
window.diagnoseMattingIssues = () => window.mattingDiagnostic.runFullDiagnosis();
window.fixMattingIssues = () => window.mattingDiagnostic.attemptAutoFix();

console.log('🔧 抠图诊断工具已加载。使用 diagnoseMattingIssues() 进行诊断，使用 fixMattingIssues() 尝试修复。');
