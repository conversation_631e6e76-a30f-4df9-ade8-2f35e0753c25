/**
 * 背景替换系统
 * 支持纯色、图片和透明背景替换，实现自适应缩放和居中显示
 */

class BackgroundReplacement {
    constructor() {
        // 背景设置
        this.backgroundType = 'transparent'; // transparent, color, image
        this.backgroundColor = '#00FF00';    // 默认绿色
        this.backgroundImage = null;         // 背景图片
        this.backgroundVideo = null;         // 背景视频
        
        // 缩放和定位设置
        this.scaleMode = 'cover';           // cover, contain, stretch, center
        this.alignment = 'center';          // center, top, bottom, left, right
        this.offsetX = 0;                   // X轴偏移
        this.offsetY = 0;                   // Y轴偏移
        
        // 图像处理设置
        this.brightness = 1.0;              // 亮度调节
        this.contrast = 1.0;                // 对比度调节
        this.saturation = 1.0;              // 饱和度调节
        this.blur = 0;                      // 模糊程度
        
        // 动画设置
        this.enableAnimation = false;       // 启用背景动画
        this.animationSpeed = 1.0;          // 动画速度
        this.animationType = 'none';        // none, pan, zoom, rotate
        
        // 工作画布
        this.bgCanvas = document.createElement('canvas');
        this.bgCtx = this.bgCanvas.getContext('2d');
        this.compositeCanvas = document.createElement('canvas');
        this.compositeCtx = this.compositeCanvas.getContext('2d');
        
        // 动画状态
        this.animationFrame = 0;
        this.animationTime = 0;
        
        // 性能监控
        this.processingTimes = [];
    }
    
    /**
     * 设置画布尺寸
     */
    setCanvasSize(width, height) {
        this.bgCanvas.width = width;
        this.bgCanvas.height = height;
        this.compositeCanvas.width = width;
        this.compositeCanvas.height = height;
    }
    
    /**
     * 设置背景
     * @param {string} type - 背景类型: transparent, color, image, video
     * @param {*} value - 背景值
     */
    setBackground(type, value) {
        this.backgroundType = type;
        
        switch (type) {
            case 'color':
                this.backgroundColor = value;
                break;
                
            case 'image':
                if (value instanceof Image) {
                    this.backgroundImage = value;
                } else if (value instanceof File) {
                    this.loadImageFromFile(value);
                } else if (typeof value === 'string') {
                    this.loadImageFromURL(value);
                }
                break;
                
            case 'video':
                if (value instanceof HTMLVideoElement) {
                    this.backgroundVideo = value;
                } else if (value instanceof File) {
                    this.loadVideoFromFile(value);
                } else if (typeof value === 'string') {
                    this.loadVideoFromURL(value);
                }
                break;
                
            case 'transparent':
            default:
                // 无需额外处理
                break;
        }
    }
    
    /**
     * 从文件加载图片
     */
    loadImageFromFile(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.backgroundImage = img;
                console.log('背景图片加载成功:', img.width, 'x', img.height);
            };
            img.onerror = () => {
                console.error('背景图片加载失败');
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
    
    /**
     * 从URL加载图片
     */
    loadImageFromURL(url) {
        const img = new Image();
        img.onload = () => {
            this.backgroundImage = img;
            console.log('背景图片加载成功:', img.width, 'x', img.height);
        };
        img.onerror = () => {
            console.error('背景图片加载失败:', url);
        };
        img.crossOrigin = 'anonymous';
        img.src = url;
    }
    
    /**
     * 从文件加载视频
     */
    loadVideoFromFile(file) {
        const video = document.createElement('video');
        video.muted = true;
        video.loop = true;
        video.autoplay = true;
        
        const url = URL.createObjectURL(file);
        video.src = url;
        
        video.onloadeddata = () => {
            this.backgroundVideo = video;
            console.log('背景视频加载成功:', video.videoWidth, 'x', video.videoHeight);
        };
        
        video.onerror = () => {
            console.error('背景视频加载失败');
            URL.revokeObjectURL(url);
        };
    }
    
    /**
     * 从URL加载视频
     */
    loadVideoFromURL(url) {
        const video = document.createElement('video');
        video.muted = true;
        video.loop = true;
        video.autoplay = true;
        video.crossOrigin = 'anonymous';
        
        video.onloadeddata = () => {
            this.backgroundVideo = video;
            console.log('背景视频加载成功:', video.videoWidth, 'x', video.videoHeight);
        };
        
        video.onerror = () => {
            console.error('背景视频加载失败:', url);
        };
        
        video.src = url;
    }
    
    /**
     * 渲染背景
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     */
    renderBackground(width, height) {
        this.setCanvasSize(width, height);
        
        // 清空背景画布
        this.bgCtx.clearRect(0, 0, width, height);
        
        switch (this.backgroundType) {
            case 'color':
                this.renderColorBackground(width, height);
                break;
                
            case 'image':
                if (this.backgroundImage) {
                    this.renderImageBackground(width, height);
                }
                break;
                
            case 'video':
                if (this.backgroundVideo) {
                    this.renderVideoBackground(width, height);
                }
                break;
                
            case 'transparent':
            default:
                // 透明背景，无需渲染
                break;
        }
        
        // 应用图像处理效果
        this.applyImageEffects();
        
        // 更新动画
        if (this.enableAnimation) {
            this.updateAnimation();
        }
    }
    
    /**
     * 渲染纯色背景
     */
    renderColorBackground(width, height) {
        this.bgCtx.fillStyle = this.backgroundColor;
        this.bgCtx.fillRect(0, 0, width, height);
    }
    
    /**
     * 渲染图片背景
     */
    renderImageBackground(width, height) {
        const img = this.backgroundImage;
        const { x, y, w, h } = this.calculateImageTransform(
            img.width, img.height, width, height
        );
        
        this.bgCtx.drawImage(img, x, y, w, h);
    }
    
    /**
     * 渲染视频背景
     */
    renderVideoBackground(width, height) {
        const video = this.backgroundVideo;
        if (video.readyState >= 2) { // HAVE_CURRENT_DATA
            const { x, y, w, h } = this.calculateImageTransform(
                video.videoWidth, video.videoHeight, width, height
            );
            
            this.bgCtx.drawImage(video, x, y, w, h);
        }
    }
    
    /**
     * 计算图像变换参数
     */
    calculateImageTransform(srcWidth, srcHeight, destWidth, destHeight) {
        let x = 0, y = 0, w = srcWidth, h = srcHeight;
        
        switch (this.scaleMode) {
            case 'cover':
                // 覆盖整个区域，保持宽高比，可能裁剪
                const coverScale = Math.max(destWidth / srcWidth, destHeight / srcHeight);
                w = srcWidth * coverScale;
                h = srcHeight * coverScale;
                x = (destWidth - w) / 2;
                y = (destHeight - h) / 2;
                break;
                
            case 'contain':
                // 包含在区域内，保持宽高比，可能有空白
                const containScale = Math.min(destWidth / srcWidth, destHeight / srcHeight);
                w = srcWidth * containScale;
                h = srcHeight * containScale;
                x = (destWidth - w) / 2;
                y = (destHeight - h) / 2;
                break;
                
            case 'stretch':
                // 拉伸填充，不保持宽高比
                w = destWidth;
                h = destHeight;
                x = 0;
                y = 0;
                break;
                
            case 'center':
                // 居中显示，不缩放
                x = (destWidth - srcWidth) / 2;
                y = (destHeight - srcHeight) / 2;
                w = srcWidth;
                h = srcHeight;
                break;
        }
        
        // 应用对齐方式调整
        switch (this.alignment) {
            case 'top':
                y = 0;
                break;
            case 'bottom':
                y = destHeight - h;
                break;
            case 'left':
                x = 0;
                break;
            case 'right':
                x = destWidth - w;
                break;
        }
        
        // 应用偏移
        x += this.offsetX;
        y += this.offsetY;
        
        return { x, y, w, h };
    }
    
    /**
     * 应用图像效果
     */
    applyImageEffects() {
        if (this.brightness === 1.0 && this.contrast === 1.0 && 
            this.saturation === 1.0 && this.blur === 0) {
            return; // 无需处理
        }
        
        const canvas = this.bgCanvas;
        const ctx = this.bgCtx;
        
        // 应用CSS滤镜
        let filter = '';
        
        if (this.brightness !== 1.0) {
            filter += `brightness(${this.brightness}) `;
        }
        
        if (this.contrast !== 1.0) {
            filter += `contrast(${this.contrast}) `;
        }
        
        if (this.saturation !== 1.0) {
            filter += `saturate(${this.saturation}) `;
        }
        
        if (this.blur > 0) {
            filter += `blur(${this.blur}px) `;
        }
        
        if (filter) {
            ctx.filter = filter.trim();
            
            // 重新绘制以应用滤镜
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.putImageData(imageData, 0, 0);
            
            // 重置滤镜
            ctx.filter = 'none';
        }
    }
    
    /**
     * 更新动画
     */
    updateAnimation() {
        this.animationFrame++;
        this.animationTime += this.animationSpeed;
        
        switch (this.animationType) {
            case 'pan':
                this.offsetX = Math.sin(this.animationTime * 0.01) * 20;
                this.offsetY = Math.cos(this.animationTime * 0.01) * 20;
                break;
                
            case 'zoom':
                const zoomFactor = 1 + Math.sin(this.animationTime * 0.02) * 0.1;
                // 这里可以实现缩放动画
                break;
                
            case 'rotate':
                // 这里可以实现旋转动画
                break;
        }
    }
    
    /**
     * 合成前景和背景
     * @param {ImageData} foregroundData - 前景图像数据
     * @param {Float32Array} maskData - 透明度遮罩
     * @param {number} width - 图像宽度
     * @param {number} height - 图像高度
     * @returns {ImageData} 合成后的图像数据
     */
    composite(foregroundData, maskData, width, height) {
        const startTime = performance.now();
        
        // 渲染背景
        this.renderBackground(width, height);
        
        // 设置合成画布
        this.compositeCanvas.width = width;
        this.compositeCanvas.height = height;
        
        // 绘制背景
        if (this.backgroundType !== 'transparent') {
            this.compositeCtx.drawImage(this.bgCanvas, 0, 0);
        }
        
        // 获取背景图像数据
        const bgImageData = this.compositeCtx.getImageData(0, 0, width, height);
        const bgData = bgImageData.data;
        const fgData = foregroundData.data;
        
        // 合成前景和背景
        for (let i = 0; i < fgData.length; i += 4) {
            const pixelIndex = Math.floor(i / 4);
            const alpha = maskData[pixelIndex];
            
            if (this.backgroundType === 'transparent') {
                // 透明背景模式 - 保持原始颜色，只设置透明度
                bgData[i] = fgData[i];                    // R
                bgData[i + 1] = fgData[i + 1];            // G
                bgData[i + 2] = fgData[i + 2];            // B

                // 优化透明度处理 - 确保边缘平滑
                let finalAlpha = alpha;
                if (alpha < 0.05) {
                    finalAlpha = 0; // 完全透明
                } else if (alpha < 0.95) {
                    // 边缘抗锯齿处理
                    finalAlpha = this.smoothAlphaTransition(alpha);
                }

                bgData[i + 3] = Math.round(finalAlpha * 255); // A
            } else {
                // 背景替换模式
                const invAlpha = 1 - alpha;
                bgData[i] = Math.round(fgData[i] * alpha + bgData[i] * invAlpha);         // R
                bgData[i + 1] = Math.round(fgData[i + 1] * alpha + bgData[i + 1] * invAlpha); // G
                bgData[i + 2] = Math.round(fgData[i + 2] * alpha + bgData[i + 2] * invAlpha); // B
                bgData[i + 3] = 255; // 完全不透明
            }
        }
        
        // 更新合成画布
        this.compositeCtx.putImageData(bgImageData, 0, 0);
        
        // 性能统计
        const processingTime = performance.now() - startTime;
        this.processingTimes.push(processingTime);
        if (this.processingTimes.length > 30) {
            this.processingTimes.shift();
        }
        
        return bgImageData;
    }

    /**
     * 平滑Alpha过渡处理（用于透明背景的边缘抗锯齿）
     * @param {number} alpha - 原始Alpha值
     * @returns {number} 平滑后的Alpha值
     */
    smoothAlphaTransition(alpha) {
        // 使用平滑步函数进行边缘抗锯齿
        if (alpha <= 0.1) return 0;
        if (alpha >= 0.9) return 1;

        // 应用平滑曲线 (smoothstep function)
        const t = (alpha - 0.1) / 0.8; // 归一化到[0,1]
        const smoothed = t * t * (3 - 2 * t);
        return Math.max(0, Math.min(1, smoothed * 0.8 + 0.1));
    }

    /**
     * 获取合成画布
     */
    getCompositeCanvas() {
        return this.compositeCanvas;
    }
    
    /**
     * 获取背景画布
     */
    getBackgroundCanvas() {
        return this.bgCanvas;
    }
    
    /**
     * 设置图像效果参数
     */
    setImageEffects(effects) {
        if (effects.brightness !== undefined) this.brightness = effects.brightness;
        if (effects.contrast !== undefined) this.contrast = effects.contrast;
        if (effects.saturation !== undefined) this.saturation = effects.saturation;
        if (effects.blur !== undefined) this.blur = effects.blur;
    }
    
    /**
     * 设置动画参数
     */
    setAnimation(params) {
        if (params.enable !== undefined) this.enableAnimation = params.enable;
        if (params.speed !== undefined) this.animationSpeed = params.speed;
        if (params.type !== undefined) this.animationType = params.type;
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        if (this.processingTimes.length === 0) {
            return { avgTime: 0, fps: 0 };
        }
        
        const avgTime = this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
        const fps = Math.round(1000 / avgTime);
        
        return {
            avgTime: avgTime.toFixed(2),
            fps: fps
        };
    }
}

// 导出模块
window.BackgroundReplacement = BackgroundReplacement;
